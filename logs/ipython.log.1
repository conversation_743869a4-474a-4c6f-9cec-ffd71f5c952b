2025-09-17 18:58:46,240 INFO ipython sample_metrics = [
    {
            "metric_name": "Employee Wellness Program Participation",
                    "esg_pillar": "Social",
                            "sdg_link": "3",
                                    "sdg_target": "3.4",
                                            "unit": "%",
                                                    "q1": 82.04,
                                                            "q2": 74.07
                                                                },
                                                                    {
                                                                            "metric_name": "Workplace Safety Incidents",
                                                                                    "esg_pillar": "Social", 
                                                                                            "sdg_link": "8",
                                                                                                    "sdg_target": "8.8",
                                                                                                            "unit": "Count",
                                                                                                                    "q1": 15,
                                                                                                                            "q2": 12
                                                                                                                                },
                                                                                                                                    {
                                                                                                                                            "metric_name": "Carbon Emissions Reduction",
                                                                                                                                                    "esg_pillar": "Environmental",
                                                                                                                                                            "sdg_link": "13",
                                                                                                                                                                    "sdg_target": "13.2",
                                                                                                                                                                            "unit": "Tons CO2",
                                                                                                                                                                                    "q1": 1200,
                                                                                                                                                                                            "q2": 1100
                                                                                                                                                                                                },
                                                                                                                                                                                                    {
                                                                                                                                                                                                            "metric_name": "Board Diversity Index",
                                                                                                                                                                                                                    "esg_pillar": "Governance",
                                                                                                                                                                                                                            "sdg_link": "5",
                                                                                                                                                                                                                                    "sdg_target": "5.5",
                                                                                                                                                                                                                                            "unit": "Score",
                                                                                                                                                                                                                                                    "q1": 75,
                                                                                                                                                                                                                                                            "q2": 80
                                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                                ]
2025-09-17 18:58:46,241 INFO ipython for metric_data in sample_metrics:
        if not frappe.db.exists("Sustainability Metric", {"metric_name": metric_data["metric_name"]}):
                    metric = frappe.new_doc("Sustainability Metric")
                            for key, value in metric_data.items():
                                            metric.set(key, value)
2025-09-17 18:58:46,241 INFO ipython         metric.insert(ignore_permissions=True)
2025-09-17 18:58:46,241 INFO ipython         print(f"Created metric: {metric_data['metric_name']}")
2025-09-17 18:58:46,241 INFO ipython print("Sample metrics created!")
2025-09-17 18:58:46,241 INFO ipython # Create sample metrics one by one
2025-09-17 18:58:46,241 INFO ipython metric1 = frappe.new_doc("Sustainability Metric")
2025-09-17 18:58:46,241 INFO ipython metric1.metric_name = "Employee Wellness Program Participation"
2025-09-17 18:58:46,241 INFO ipython metric1.esg_pillar = "Social"
2025-09-17 18:58:46,241 INFO ipython metric1.sdg_link = "3"
2025-09-17 18:58:46,241 INFO ipython metric1.sdg_target = "3.4"
2025-09-17 18:58:46,241 INFO ipython metric1.unit = "%"
2025-09-17 18:58:46,242 INFO ipython metric1.q1 = 82.04
2025-09-17 18:58:46,242 INFO ipython metric1.q2 = 74.07
2025-09-17 18:58:46,242 INFO ipython metric1.insert(ignore_permissions=True)
2025-09-17 18:58:46,242 INFO ipython print("Created: Employee Wellness Program Participation")
2025-09-17 18:58:46,242 INFO ipython # Create simple metrics without linked fields
2025-09-17 18:58:46,242 INFO ipython metric1 = frappe.new_doc("Sustainability Metric")
2025-09-17 18:58:46,242 INFO ipython metric1.metric_name = "Employee Wellness Program Participation"
2025-09-17 18:58:46,242 INFO ipython metric1.esg_pillar = "Social"
2025-09-17 18:58:46,242 INFO ipython metric1.unit = "%"
2025-09-17 18:58:46,242 INFO ipython metric1.q1 = 82.04
2025-09-17 18:58:46,243 INFO ipython metric1.q2 = 74.07
2025-09-17 18:58:46,243 INFO ipython metric1.insert(ignore_permissions=True)
2025-09-17 18:58:46,243 INFO ipython print("Created: Employee Wellness Program Participation")
2025-09-17 18:58:46,243 INFO ipython # Check the Sustainability Metric doctype structure
2025-09-17 18:58:46,243 INFO ipython meta = frappe.get_meta("Sustainability Metric")
2025-09-17 18:58:46,243 INFO ipython for field in meta.fields:
        if field.fieldtype in ["Link", "Select"]:
                    print(f"{field.fieldname}: {field.fieldtype} - {field.options}")
                        elif field.fieldtype in ["Data", "Float", "Int"]:
                                    print(f"{field.fieldname}: {field.fieldtype}")
2025-09-17 18:58:46,243 INFO ipython meta = frappe.get_meta("Sustainability Metric")
2025-09-17 18:58:46,243 INFO ipython for field in meta.fields:
        if field.fieldtype in ["Link", "Select"]:
                    print(f"{field.fieldname}: {field.fieldtype} - {field.options}")
                        elif field.fieldtype in ["Data", "Float", "Int"]:
                                    print(f"{field.fieldname}: {field.fieldtype}")
2025-09-17 18:58:46,243 INFO ipython frappe.db.commit()
2025-09-17 18:58:46,243 INFO ipython print("✅ All dashboard changes committed!")
2025-09-17 18:58:46,243 INFO ipython print("\n🎯 Fixed SDG Dashboard Charts!")
2025-09-17 18:58:46,243 INFO ipython print("\nThe dashboards now use working chart types:")
2025-09-17 18:58:46,243 INFO ipython print("1. SDG Mapping Dashboard - ESG Pillar Distribution (Bar Chart)")
2025-09-17 18:58:46,243 INFO ipython print("2. SDG Progress Dashboard - Sustainability Entries Trend (Line Chart)")
2025-09-17 18:58:46,243 INFO ipython print("3. SDG Distribution Dashboard - SDG Goals Distribution (Pie Chart)")
2025-09-17 18:58:46,244 INFO ipython print("\nAccess them at:")
2025-09-17 18:58:46,244 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Mapping%20Dashboard")
2025-09-17 18:58:46,244 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Progress%20Dashboard")
2025-09-17 18:58:46,244 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Distribution%20Dashboard")
2025-09-17 18:58:46,244 INFO ipython === session end ===
2025-09-17 19:03:34,284 INFO ipython === bench console session ===
2025-09-17 19:03:34,284 INFO ipython # Create report-based charts that work properly by using Custom chart type
2025-09-17 19:03:34,284 INFO ipython import frappe
2025-09-17 19:03:34,285 INFO ipython # Delete the current working charts and replace with report-based ones
2025-09-17 19:03:34,285 INFO ipython charts_to_replace = [
    ("ESG Pillar Distribution", "SDG Metrics Mapping"),
        ("Sustainability Entries Trend", "SDG Progress Tracking"), 
            ("SDG Goals Distribution", "SDG Goal Distribution")
            ]
2025-09-17 19:03:34,285 INFO ipython for old_chart, report_name in charts_to_replace:
        # Create new report-based chart
            new_chart_name = f"{report_name} Report Chart"
                
2025-09-17 19:03:34,285 INFO ipython     if not frappe.db.exists("Dashboard Chart", new_chart_name):
            chart = frappe.new_doc("Dashboard Chart")
                chart.name = new_chart_name
2025-09-17 19:03:34,285 INFO ipython         chart.chart_name = new_chart_name
2025-09-17 19:03:34,285 INFO ipython         chart.chart_type = "Custom"  # Use Custom instead of Report
2025-09-17 19:03:34,286 INFO ipython         chart.custom_options = f'''{{
            "type": "bar",
                "data": {{
                        "labels": ["SDG 3", "SDG 5", "SDG 8", "SDG 13"],
                                "datasets": [{{
                                            "label": "Metrics Count",
                                                        "data": [8, 2, 3, 1],
                                                                    "backgroundColor": ["#3b82f6", "#10b981", "#f59e0b", "#ef4444"]
                                                                            }}]
                                                                                }},
                                                                                    "options": {{
                                                                                            "responsive": true,
                                                                                                    "plugins": {{
                                                                                                                "title": {{
                                                                                                                                "display": true,
                                                                                                                                                "text": "{report_name}"
                                                                                                                                                            }}
                                                                                                                                                                    }}
                                                                                                                                                                        }}
                                                                                                                                                                        }}'''
2025-09-17 19:03:34,286 INFO ipython         chart.type = "Bar" if "Mapping" in report_name else ("Line" if "Progress" in report_name else "Pie")
2025-09-17 19:03:34,286 INFO ipython         chart.color = "#3b82f6"
2025-09-17 19:03:34,286 INFO ipython         chart.is_public = 1
2025-09-17 19:03:34,286 INFO ipython         chart.module = "Sdg Reporting"
2025-09-17 19:03:34,286 INFO ipython         chart.is_standard = 1
2025-09-17 19:03:34,286 INFO ipython         chart.filters_json = "[]"
2025-09-17 19:03:34,286 INFO ipython         chart.insert(ignore_permissions=True)
2025-09-17 19:03:34,287 INFO ipython         print(f"✅ Created {new_chart_name}")
2025-09-17 19:03:34,287 INFO ipython print("Report-based charts created!")
2025-09-17 19:03:34,287 INFO ipython === session end ===
2025-09-17 19:06:44,135 INFO ipython === bench console session ===
2025-09-17 19:06:44,136 INFO ipython # Update the existing dashboards to include links to the custom SDG dashboard
2025-09-17 19:06:44,136 INFO ipython import frappe
2025-09-17 19:06:44,137 INFO ipython # Create a new number card that links to the custom dashboard
2025-09-17 19:06:44,137 INFO ipython if not frappe.db.exists("Number Card", "SDG Dashboard Link"):
        card = frappe.new_doc("Number Card")
            card.name = "SDG Dashboard Link"
2025-09-17 19:06:44,139 INFO ipython     card.label = "View Full SDG Dashboard"
2025-09-17 19:06:44,139 INFO ipython     card.type = "Custom"
2025-09-17 19:06:44,139 INFO ipython     card.method = "sdg_reporting.sdg_reporting.api.dashboard.get_total_metrics"
2025-09-17 19:06:44,141 INFO ipython     card.color = "#3b82f6"
2025-09-17 19:06:44,141 INFO ipython     card.is_public = 1
2025-09-17 19:06:44,141 INFO ipython     card.module = "Sdg Reporting"
2025-09-17 19:06:44,141 INFO ipython     card.is_standard = 1
2025-09-17 19:06:44,141 INFO ipython     card.insert(ignore_permissions=True)
2025-09-17 19:06:44,141 INFO ipython     print("✅ Created SDG Dashboard Link card")
2025-09-17 19:06:44,142 INFO ipython # Update one of the dashboards to include this link
2025-09-17 19:06:44,142 INFO ipython dashboard = frappe.get_doc("Dashboard", "SDG Mapping Dashboard")
2025-09-17 19:06:44,142 INFO ipython dashboard.dashboard_name = "SDG Reporting Dashboard (Full View Available)"
2025-09-17 19:06:44,142 INFO ipython # Add the link card
2025-09-17 19:06:44,142 INFO ipython link_card_exists = False
2025-09-17 19:06:44,142 INFO ipython for card in dashboard.cards:
        if card.card == "SDG Dashboard Link":
                    link_card_exists = True
                            break
2025-09-17 19:06:44,142 INFO ipython if not link_card_exists:
        card_row = dashboard.append("cards")
            card_row.card = "SDG Dashboard Link"
2025-09-17 19:06:44,142 INFO ipython dashboard.save(ignore_permissions=True)
2025-09-17 19:06:44,142 INFO ipython print("✅ Updated SDG Mapping Dashboard with link to full dashboard")
2025-09-17 19:06:44,143 INFO ipython frappe.db.commit()
2025-09-17 19:06:44,143 INFO ipython print("\n🎯 SDG Dashboard Setup Complete!")
2025-09-17 19:06:44,145 INFO ipython print("\nYou now have:")
2025-09-17 19:06:44,145 INFO ipython print("1. Standard Frappe Dashboards with working charts")
2025-09-17 19:06:44,146 INFO ipython print("2. Custom SDG Dashboard with exact format from your reference")
2025-09-17 19:06:44,146 INFO ipython print("\nAccess the custom dashboard at:")
2025-09-17 19:06:44,146 INFO ipython print("http://localhost:8000/sdg_dashboard")
2025-09-17 19:06:44,146 INFO ipython print("\nOr use the standard dashboards at:")
2025-09-17 19:06:44,147 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Mapping%20Dashboard")
2025-09-17 19:06:44,147 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Progress%20Dashboard")
2025-09-17 19:06:44,147 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Distribution%20Dashboard")
2025-09-17 19:06:44,147 INFO ipython === session end ===
2025-09-17 19:20:05,223 INFO ipython === bench console session ===
2025-09-17 19:20:05,223 INFO ipython # Create proper report-based dashboard charts following ERPNext pattern
2025-09-17 19:20:05,224 INFO ipython import frappe
2025-09-17 19:20:05,224 INFO ipython import json
2025-09-17 19:20:05,224 INFO ipython # Delete existing problematic charts first
2025-09-17 19:20:05,224 INFO ipython charts_to_delete = [
    "SDG Metrics Mapping Chart",
        "SDG Progress Tracking Chart", 
            "SDG Goal Distribution Chart",
                "SDG Metrics Mapping Report Chart",
                    "SDG Progress Tracking Report Chart",
                        "SDG Goal Distribution Report Chart"
                        ]
2025-09-17 19:20:05,224 INFO ipython for chart_name in charts_to_delete:
        if frappe.db.exists("Dashboard Chart", chart_name):
                    frappe.delete_doc("Dashboard Chart", chart_name, ignore_permissions=True)
                            print(f"🗑️ Deleted {chart_name}")
2025-09-17 19:20:05,224 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,225 INFO ipython print("✅ Cleaned up old charts")
2025-09-17 19:20:05,225 INFO ipython # Create new report-based charts following ERPNext pattern
2025-09-17 19:20:05,226 INFO ipython charts_config = [
    {
            "name": "SDG Metrics by ESG Pillar",
                    "chart_name": "SDG Metrics by ESG Pillar", 
                            "chart_type": "Report",
                                    "report_name": "SDG Metrics Mapping",
                                            "x_field": "esg_pillar",
                                                    "y_axis": [
                                                                {
                                                                                "y_field": "metric_count",
                                                                                                "doctype": "Dashboard Chart Field"
                                                                                                            }
                                                                                                                    ],
                                                                                                                            "type": "Bar",
                                                                                                                                    "color": "#3b82f6",
                                                                                                                                            "is_public": 1,
                                                                                                                                                    "module": "Sdg Reporting",
                                                                                                                                                            "is_standard": 1,
                                                                                                                                                                    "filters_json": "[]",
                                                                                                                                                                            "use_report_chart": 0
                                                                                                                                                                                },
                                                                                                                                                                                    {
                                                                                                                                                                                            "name": "SDG Progress Q1 vs Q2",
                                                                                                                                                                                                    "chart_name": "SDG Progress Q1 vs Q2",
                                                                                                                                                                                                            "chart_type": "Report", 
                                                                                                                                                                                                                    "report_name": "SDG Progress Tracking",
                                                                                                                                                                                                                            "x_field": "metric",
                                                                                                                                                                                                                                    "y_axis": [
                                                                                                                                                                                                                                                {
                                                                                                                                                                                                                                                                "y_field": "q1",
                                                                                                                                                                                                                                                                                "doctype": "Dashboard Chart Field"
                                                                                                                                                                                                                                                                                            },
                                                                                                                                                                                                                                                                                                        {
                                                                                                                                                                                                                                                                                                                        "y_field": "q2", 
                                                                                                                                                                                                                                                                                                                                        "doctype": "Dashboard Chart Field"
                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                            ],
                                                                                                                                                                                                                                                                                                                                                                    "type": "Bar",
                                                                                                                                                                                                                                                                                                                                                                            "color": "#10b981",
                                                                                                                                                                                                                                                                                                                                                                                    "is_public": 1,
                                                                                                                                                                                                                                                                                                                                                                                            "module": "Sdg Reporting",
                                                                                                                                                                                                                                                                                                                                                                                                    "is_standard": 1,
                                                                                                                                                                                                                                                                                                                                                                                                            "filters_json": "[]",
                                                                                                                                                                                                                                                                                                                                                                                                                    "use_report_chart": 0
                                                                                                                                                                                                                                                                                                                                                                                                                        },
                                                                                                                                                                                                                                                                                                                                                                                                                            {
                                                                                                                                                                                                                                                                                                                                                                                                                                    "name": "SDG Goal Distribution",
                                                                                                                                                                                                                                                                                                                                                                                                                                            "chart_name": "SDG Goal Distribution",
                                                                                                                                                                                                                                                                                                                                                                                                                                                    "chart_type": "Report",
                                                                                                                                                                                                                                                                                                                                                                                                                                                            "report_name": "SDG Goal Distribution", 
                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "x_field": "target_name",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "y_axis": [
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        "y_field": "employee_impact",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        "doctype": "Dashboard Chart Field"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "type": "Pie",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "color": "#f59e0b",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "is_public": 1,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "module": "Sdg Reporting", 
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "is_standard": 1,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            "filters_json": "[]",
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    "use_report_chart": 0
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ]
2025-09-17 19:20:05,226 INFO ipython # Create the charts
2025-09-17 19:20:05,226 INFO ipython for config in charts_config:
        chart = frappe.new_doc("Dashboard Chart")
            
2025-09-17 19:20:05,226 INFO ipython     # Set basic fields
2025-09-17 19:20:05,226 INFO ipython     chart.name = config["name"]
2025-09-17 19:20:05,227 INFO ipython     chart.chart_name = config["chart_name"]
2025-09-17 19:20:05,227 INFO ipython     chart.chart_type = config["chart_type"]
2025-09-17 19:20:05,227 INFO ipython     chart.report_name = config["report_name"]
2025-09-17 19:20:05,227 INFO ipython     chart.x_field = config["x_field"]
2025-09-17 19:20:05,227 INFO ipython     chart.type = config["type"]
2025-09-17 19:20:05,227 INFO ipython     chart.color = config["color"]
2025-09-17 19:20:05,227 INFO ipython     chart.is_public = config["is_public"]
2025-09-17 19:20:05,228 INFO ipython     chart.module = config["module"]
2025-09-17 19:20:05,228 INFO ipython     chart.is_standard = config["is_standard"]
2025-09-17 19:20:05,228 INFO ipython     chart.filters_json = config["filters_json"]
2025-09-17 19:20:05,228 INFO ipython     chart.use_report_chart = config["use_report_chart"]
2025-09-17 19:20:05,228 INFO ipython     # Add y_axis fields
2025-09-17 19:20:05,228 INFO ipython     for y_field_config in config["y_axis"]:
            y_field_row = chart.append("y_axis")
                y_field_row.y_field = y_field_config["y_field"]
2025-09-17 19:20:05,228 INFO ipython     chart.insert(ignore_permissions=True)
2025-09-17 19:20:05,228 INFO ipython     print(f"✅ Created {config['name']}")
2025-09-17 19:20:05,228 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,228 INFO ipython print("\n🎯 Report-based Dashboard Charts Created Successfully!")
2025-09-17 19:20:05,229 INFO ipython print("\nCharts created:")
2025-09-17 19:20:05,229 INFO ipython print("1. SDG Metrics by ESG Pillar (Bar Chart)")
2025-09-17 19:20:05,229 INFO ipython print("2. SDG Progress Q1 vs Q2 (Bar Chart)")  
2025-09-17 19:20:05,229 INFO ipython print("3. SDG Goal Distribution (Pie Chart)")
2025-09-17 19:20:05,229 INFO ipython print("\nThese charts use the proper ERPNext pattern with:")
2025-09-17 19:20:05,229 INFO ipython print("- chart_type: 'Report'")
2025-09-17 19:20:05,229 INFO ipython print("- x_field and y_axis configuration")
2025-09-17 19:20:05,229 INFO ipython print("- Proper numeric field mapping")
2025-09-17 19:20:05,229 INFO ipython # Create the remaining charts properly
2025-09-17 19:20:05,230 INFO ipython remaining_charts = [
    {
            "name": "SDG Metrics by ESG Pillar",
                    "chart_name": "SDG Metrics by ESG Pillar", 
                            "chart_type": "Report",
                                    "report_name": "SDG Metrics Mapping",
                                            "x_field": "esg_pillar",
                                                    "y_field": "metric_count",
                                                            "type": "Bar",
                                                                    "color": "#3b82f6"
                                                                        },
                                                                            {
                                                                                    "name": "SDG Progress Q1 vs Q2",
                                                                                            "chart_name": "SDG Progress Q1 vs Q2",
                                                                                                    "chart_type": "Report", 
                                                                                                            "report_name": "SDG Progress Tracking",
                                                                                                                    "x_field": "metric",
                                                                                                                            "y_field": "q1",
                                                                                                                                    "type": "Bar",
                                                                                                                                            "color": "#10b981"
                                                                                                                                                }
                                                                                                                                                ]
2025-09-17 19:20:05,230 INFO ipython for config in remaining_charts:
        if not frappe.db.exists("Dashboard Chart", config["name"]):
                    chart = frappe.new_doc("Dashboard Chart")
                            chart.name = config["name"]
2025-09-17 19:20:05,230 INFO ipython         chart.chart_name = config["chart_name"]
2025-09-17 19:20:05,230 INFO ipython         chart.chart_type = config["chart_type"]
2025-09-17 19:20:05,230 INFO ipython         chart.report_name = config["report_name"]
2025-09-17 19:20:05,230 INFO ipython         chart.x_field = config["x_field"]
2025-09-17 19:20:05,231 INFO ipython         chart.type = config["type"]
2025-09-17 19:20:05,231 INFO ipython         chart.color = config["color"]
2025-09-17 19:20:05,231 INFO ipython         chart.is_public = 1
2025-09-17 19:20:05,231 INFO ipython         chart.module = "Sdg Reporting"
2025-09-17 19:20:05,231 INFO ipython         chart.is_standard = 1
2025-09-17 19:20:05,231 INFO ipython         chart.filters_json = "[]"
2025-09-17 19:20:05,231 INFO ipython         chart.use_report_chart = 0
2025-09-17 19:20:05,231 INFO ipython         # Add y_axis field
2025-09-17 19:20:05,231 INFO ipython         y_field_row = chart.append("y_axis")
2025-09-17 19:20:05,232 INFO ipython         y_field_row.y_field = config["y_field"]
2025-09-17 19:20:05,232 INFO ipython         chart.insert(ignore_permissions=True)
2025-09-17 19:20:05,232 INFO ipython         print(f"✅ Created {config['name']}")
2025-09-17 19:20:05,232 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,232 INFO ipython print("✅ All charts created successfully!")
2025-09-17 19:20:05,232 INFO ipython # Create charts with unique names
2025-09-17 19:20:05,232 INFO ipython unique_charts = [
    {
            "name": "ESG Pillar Metrics Count",
                    "chart_name": "ESG Pillar Metrics Count", 
                            "chart_type": "Report",
                                    "report_name": "SDG Metrics Mapping",
                                            "x_field": "esg_pillar",
                                                    "y_field": "metric_count",
                                                            "type": "Bar",
                                                                    "color": "#3b82f6"
                                                                        },
                                                                            {
                                                                                    "name": "Q1 vs Q2 Progress",
                                                                                            "chart_name": "Q1 vs Q2 Progress",
                                                                                                    "chart_type": "Report", 
                                                                                                            "report_name": "SDG Progress Tracking",
                                                                                                                    "x_field": "metric",
                                                                                                                            "y_field": "q1",
                                                                                                                                    "type": "Bar",
                                                                                                                                            "color": "#10b981"
                                                                                                                                                }
                                                                                                                                                ]
2025-09-17 19:20:05,232 INFO ipython for config in unique_charts:
        if not frappe.db.exists("Dashboard Chart", config["name"]):
                    chart = frappe.new_doc("Dashboard Chart")
                            chart.name = config["name"]
2025-09-17 19:20:05,232 INFO ipython         chart.chart_name = config["chart_name"]
2025-09-17 19:20:05,233 INFO ipython         chart.chart_type = config["chart_type"]
2025-09-17 19:20:05,233 INFO ipython         chart.report_name = config["report_name"]
2025-09-17 19:20:05,233 INFO ipython         chart.x_field = config["x_field"]
2025-09-17 19:20:05,233 INFO ipython         chart.type = config["type"]
2025-09-17 19:20:05,233 INFO ipython         chart.color = config["color"]
2025-09-17 19:20:05,233 INFO ipython         chart.is_public = 1
2025-09-17 19:20:05,233 INFO ipython         chart.module = "Sdg Reporting"
2025-09-17 19:20:05,233 INFO ipython         chart.is_standard = 1
2025-09-17 19:20:05,233 INFO ipython         chart.filters_json = "[]"
2025-09-17 19:20:05,234 INFO ipython         chart.use_report_chart = 0
2025-09-17 19:20:05,234 INFO ipython         # Add y_axis field
2025-09-17 19:20:05,238 INFO ipython         y_field_row = chart.append("y_axis")
2025-09-17 19:20:05,239 INFO ipython         y_field_row.y_field = config["y_field"]
2025-09-17 19:20:05,239 INFO ipython         chart.insert(ignore_permissions=True)
2025-09-17 19:20:05,239 INFO ipython         print(f"✅ Created {config['name']}")
2025-09-17 19:20:05,239 INFO ipython     else:
            print(f"⚠️ {config['name']} already exists")
2025-09-17 19:20:05,239 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,239 INFO ipython print("✅ Charts creation completed!")
2025-09-17 19:20:05,239 INFO ipython # Update dashboards to use the new charts
2025-09-17 19:20:05,241 INFO ipython dashboards_to_update = [
    {
            "name": "SDG Mapping Dashboard",
                    "charts": ["ESG Pillar Metrics Count"]
                        },
                            {
                                    "name": "SDG Progress Dashboard", 
                                            "charts": ["Q1 vs Q2 Progress"]
                                                },
                                                    {
                                                            "name": "SDG Distribution Dashboard",
                                                                    "charts": ["SDG Goal Distribution"]
                                                                        }
                                                                        ]
2025-09-17 19:20:05,241 INFO ipython for dashboard_config in dashboards_to_update:
        if frappe.db.exists("Dashboard", dashboard_config["name"]):
                    dashboard = frappe.get_doc("Dashboard", dashboard_config["name"])
                            
2025-09-17 19:20:05,241 INFO ipython         # Clear existing charts
2025-09-17 19:20:05,241 INFO ipython         dashboard.charts = []
2025-09-17 19:20:05,241 INFO ipython         # Add new charts
2025-09-17 19:20:05,241 INFO ipython         for chart_name in dashboard_config["charts"]:
                if frappe.db.exists("Dashboard Chart", chart_name):
                            chart_row = dashboard.append("charts")
                                    chart_row.chart = chart_name
2025-09-17 19:20:05,241 INFO ipython                 chart_row.width = "Full"
2025-09-17 19:20:05,242 INFO ipython         dashboard.save(ignore_permissions=True)
2025-09-17 19:20:05,242 INFO ipython         print(f"✅ Updated {dashboard_config['name']} with charts: {', '.join(dashboard_config['charts'])}")
2025-09-17 19:20:05,243 INFO ipython frappe.db.commit()
2025-09-17 19:20:05,243 INFO ipython print("\n🎯 Dashboard Update Complete!")
2025-09-17 19:20:05,244 INFO ipython print("\nYour SDG dashboards now use proper report-based charts:")
2025-09-17 19:20:05,244 INFO ipython print("1. SDG Mapping Dashboard - Shows metrics count by ESG Pillar")
2025-09-17 19:20:05,244 INFO ipython print("2. SDG Progress Dashboard - Shows Q1 vs Q2 progress")  
2025-09-17 19:20:05,244 INFO ipython print("3. SDG Distribution Dashboard - Shows goal distribution")
2025-09-17 19:20:05,244 INFO ipython print("\nAccess them at:")
2025-09-17 19:20:05,244 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Mapping%20Dashboard")
2025-09-17 19:20:05,244 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Progress%20Dashboard")
2025-09-17 19:20:05,246 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Distribution%20Dashboard")
2025-09-17 19:20:05,246 INFO ipython === session end ===
2025-09-18 14:36:43,277 INFO ipython === bench console session ===
2025-09-18 14:36:43,287 INFO ipython import clearing.clearing.doctype.clearing_charges.clearing_charges
2025-09-18 14:36:43,287 INFO ipython print("Import successful")
2025-09-18 14:36:43,287 INFO ipython === session end ===
2025-09-18 14:38:32,870 INFO ipython === bench console session ===
2025-09-18 14:38:32,870 INFO ipython import frappe
2025-09-18 14:38:32,870 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:38:32,870 INFO ipython print(clearing_charges)
2025-09-18 14:38:32,871 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:38:32,871 INFO ipython print(clearing_charges)
2025-09-18 14:38:32,871 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status"], limit=5)
2025-09-18 14:38:32,871 INFO ipython print(clearing_charges)
2025-09-18 14:38:32,871 INFO ipython === session end ===
2025-09-18 14:40:54,042 INFO ipython === bench console session ===
2025-09-18 14:40:54,042 INFO ipython import frappe
2025-09-18 14:40:54,042 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import update_clearing_charges_status_from_invoice
2025-09-18 14:40:54,042 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:40:54,043 INFO ipython print(clearing_charges)
2025-09-18 14:40:54,043 INFO ipython frappe.reload_doctype("Clearing Charges")
2025-09-18 14:40:54,043 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-18 14:40:54,043 INFO ipython print(clearing_charges)
2025-09-18 14:40:54,043 INFO ipython === session end ===
2025-09-18 14:43:32,027 INFO ipython === bench console session ===
2025-09-18 14:43:32,027 INFO ipython import frappe
2025-09-18 14:43:32,028 INFO ipython # Test that our hook function works
2025-09-18 14:43:32,028 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import update_clearing_charges_status_from_invoice
2025-09-18 14:43:32,028 INFO ipython # Create a mock invoice object to test the function
2025-09-18 14:43:32,028 INFO ipython class MockInvoice:
        def __init__(self, name, status):
                    self.name = name
                            self.status = status
2025-09-18 14:43:32,028 INFO ipython mock_invoice = MockInvoice("TEST-INV-001", "Paid")
2025-09-18 14:43:32,028 INFO ipython print(f"Testing with mock invoice: {mock_invoice.name}, Status: {mock_invoice.status}")
2025-09-18 14:43:32,029 INFO ipython # Test the function (it should handle the case where no clearing charges exist gracefully)
2025-09-18 14:43:32,029 INFO ipython try:
        update_clearing_charges_status_from_invoice(mock_invoice, "on_update")
            print("✅ Function executed successfully (no clearing charges found, which is expected)")
2025-09-18 14:43:32,029 INFO ipython except Exception as e:
        print(f"❌ Error: {e}")
2025-09-18 14:43:32,029 INFO ipython class MockInvoice:
        def __init__(self, name, status):
                    self.name = name
                            self.status = status
2025-09-18 14:43:32,029 INFO ipython mock_invoice = MockInvoice("TEST-INV-001", "Paid")
2025-09-18 14:43:32,029 INFO ipython print(f"Testing with mock invoice: {mock_invoice.name}, Status: {mock_invoice.status}")
2025-09-18 14:43:32,029 INFO ipython try:
        update_clearing_charges_status_from_invoice(mock_invoice, "on_update")
            print("✅ Function executed successfully (no clearing charges found, which is expected)")
2025-09-18 14:43:32,029 INFO ipython except Exception as e:
        print(f"❌ Error: {e}")
2025-09-18 14:43:32,029 INFO ipython === session end ===
2025-09-19 14:55:28,362 INFO ipython === bench console session ===
2025-09-19 14:55:28,363 INFO ipython import frappe
2025-09-19 14:55:28,363 INFO ipython # Check if clearing charges exist
2025-09-19 14:55:28,363 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-19 14:55:28,363 INFO ipython print("Clearing Charges found:", len(clearing_charges))
2025-09-19 14:55:28,363 INFO ipython for cc in clearing_charges:
        print(f"  {cc.name}: Status={cc.status}, Invoice={cc.reference_number}")
        for cc in clearing_charges:
                print(f"  {cc.name}: Status={cc.status}, Invoice={cc.reference_number}")
                
2025-09-19 14:55:28,363 INFO ipython # Test the hook function
2025-09-19 14:55:28,364 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import handle_invoice_status_change, update_clearing_charges_from_invoice_change
2025-09-19 14:55:28,364 INFO ipython # Test with a real invoice
2025-09-19 14:55:28,364 INFO ipython invoice_name = "ACC-SINV-2025-00051"
2025-09-19 14:55:28,364 INFO ipython invoice = frappe.get_doc("Sales Invoice", invoice_name)
2025-09-19 14:55:28,364 INFO ipython print(f"Invoice {invoice_name} current status: {invoice.status}")
2025-09-19 14:55:28,364 INFO ipython # Test the update function directly
2025-09-19 14:55:28,364 INFO ipython result = update_clearing_charges_from_invoice_change(invoice_name, invoice.status)
2025-09-19 14:55:28,364 INFO ipython print(f"Update result: {result}")
2025-09-19 14:55:28,364 INFO ipython # Check if the status was updated
2025-09-19 14:55:28,365 INFO ipython cc_after = frappe.get_all("Clearing Charges", filters={"reference_number": invoice_name}, fields=["name", "status"])
2025-09-19 14:55:28,365 INFO ipython print("Clearing charges after update:")
2025-09-19 14:55:28,365 INFO ipython for cc in cc_after:
        print(f"  {cc.name}: Status={cc.status}")
        
2025-09-19 14:55:28,365 INFO ipython # Now test the hook function
2025-09-19 14:55:28,365 INFO ipython print("\nTesting hook function...")
2025-09-19 14:55:28,365 INFO ipython handle_invoice_status_change(invoice, "on_update_after_submit")
2025-09-19 14:55:28,365 INFO ipython print("Hook function executed")
2025-09-19 14:55:28,365 INFO ipython # Let's test if the hooks are properly registered
2025-09-19 14:55:28,365 INFO ipython import frappe.hooks
2025-09-19 14:55:28,366 INFO ipython # Check if our hooks are registered
2025-09-19 14:55:28,366 INFO ipython hooks = frappe.get_hooks()
2025-09-19 14:55:28,366 INFO ipython print("Sales Invoice hooks:")
2025-09-19 14:55:28,366 INFO ipython if "Sales Invoice" in hooks.get("doc_events", {}):
        for event, methods in hooks["doc_events"]["Sales Invoice"].items():
                    print(f"  {event}: {methods}")
                    else:
                            print("  No Sales Invoice hooks found!")
2025-09-19 14:55:28,366 INFO ipython # Let's also check the clearing app hooks specifically
2025-09-19 14:55:28,366 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 14:55:28,366 INFO ipython print("\nClearing app doc_events:")
2025-09-19 14:55:28,366 INFO ipython if hasattr(clearing_hooks, 'doc_events'):
        print(clearing_hooks.doc_events.get("Sales Invoice", "Not found"))
        else:
                print("No doc_events found in clearing hooks")
2025-09-19 14:55:28,366 INFO ipython # Check hooks properly
2025-09-19 14:55:28,366 INFO ipython hooks = frappe.get_hooks()
2025-09-19 14:55:28,367 INFO ipython if "Sales Invoice" in hooks.get("doc_events", {}):
        for event, methods in hooks["doc_events"]["Sales Invoice"].items():
                    print(f"  {event}: {methods}")
                    else:
                            print("  No Sales Invoice hooks found!")
2025-09-19 14:55:28,367 INFO ipython # Check clearing hooks
2025-09-19 14:55:28,367 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 14:55:28,367 INFO ipython if hasattr(clearing_hooks, 'doc_events'):
        si_hooks = clearing_hooks.doc_events.get("Sales Invoice", "Not found")
            print(f"\nClearing Sales Invoice hooks: {si_hooks}")
2025-09-19 14:55:28,367 INFO ipython else:
        print("No doc_events found in clearing hooks")
2025-09-19 14:55:28,367 INFO ipython === session end ===
2025-09-19 16:49:22,959 INFO ipython === bench console session ===
2025-09-19 16:49:22,960 INFO ipython import frappe
2025-09-19 16:49:22,960 INFO ipython # Check if hooks are registered
2025-09-19 16:49:22,961 INFO ipython hooks = frappe.get_hooks()
2025-09-19 16:49:22,961 INFO ipython print("Checking Sales Invoice hooks...")
2025-09-19 16:49:22,961 INFO ipython if "Sales Invoice" in hooks.get("doc_events", {}):
        print("Sales Invoice hooks found:")
            for event, methods in hooks["doc_events"]["Sales Invoice"].items():
                        print(f"  {event}: {methods}")
2025-09-19 16:49:22,961 INFO ipython else:
        print("No Sales Invoice hooks found in system!")
2025-09-19 16:49:22,962 INFO ipython # Check clearing app hooks
2025-09-19 16:49:22,962 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 16:49:22,962 INFO ipython print(f"\nClearing app Sales Invoice hooks:")
2025-09-19 16:49:22,962 INFO ipython if hasattr(clearing_hooks, 'doc_events') and "Sales Invoice" in clearing_hooks.doc_events:
        print(clearing_hooks.doc_events["Sales Invoice"])
        else:
                print("Not found in clearing hooks")
2025-09-19 16:49:22,963 INFO ipython # Simple check
2025-09-19 16:49:22,963 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 16:49:22,963 INFO ipython print("Clearing hooks doc_events:")
2025-09-19 16:49:22,963 INFO ipython print(clearing_hooks.doc_events)
2025-09-19 16:49:22,963 INFO ipython # Check if our function exists
2025-09-19 16:49:22,964 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import handle_invoice_status_change
2025-09-19 16:49:22,964 INFO ipython print("\nFunction imported successfully")
2025-09-19 16:49:22,964 INFO ipython # Let's test if the hooks are working by simulating an invoice status change
2025-09-19 16:49:22,964 INFO ipython invoice_name = "ACC-SINV-2025-00051"
2025-09-19 16:49:22,964 INFO ipython invoice = frappe.get_doc("Sales Invoice", invoice_name)
2025-09-19 16:49:22,964 INFO ipython print(f"Current invoice status: {invoice.status}")
2025-09-19 16:49:22,964 INFO ipython # Let's manually change the status and see if it triggers
2025-09-19 16:49:22,965 INFO ipython old_status = invoice.status
2025-09-19 16:49:22,965 INFO ipython invoice.status = "Paid"
2025-09-19 16:49:22,965 INFO ipython print(f"Changed status to: {invoice.status}")
2025-09-19 16:49:22,965 INFO ipython # Test the hook function manually
2025-09-19 16:49:22,965 INFO ipython handle_invoice_status_change(invoice, "on_update_after_submit")
2025-09-19 16:49:22,965 INFO ipython # Check if clearing charges were updated
2025-09-19 16:49:22,966 INFO ipython cc_updated = frappe.get_all("Clearing Charges", filters={"reference_number": invoice_name}, fields=["name", "status"])
2025-09-19 16:49:22,966 INFO ipython print(f"Clearing charges status after manual hook call:")
2025-09-19 16:49:22,966 INFO ipython for cc in cc_updated:
        print(f"  {cc.name}: {cc.status}")
        
2025-09-19 16:49:22,966 INFO ipython # Restore original status
2025-09-19 16:49:22,966 INFO ipython invoice.status = old_status
2025-09-19 16:49:22,966 INFO ipython === session end ===
2025-09-19 17:14:23,436 INFO ipython === bench console session ===
2025-09-19 17:14:23,437 INFO ipython import frappe
2025-09-19 17:14:23,437 INFO ipython # Test the new transit document checking functionality
2025-09-19 17:14:23,437 INFO ipython clearing_files = frappe.get_all("Clearing File", fields=["name", "status"], limit=3)
2025-09-19 17:14:23,438 INFO ipython print("Available clearing files:")
2025-09-19 17:14:23,442 INFO ipython for cf in clearing_files:
        print(f"  {cf.name}: Status={cf.status}")
        
2025-09-19 17:14:23,442 INFO ipython # Test with the first one if available
2025-09-19 17:14:23,443 INFO ipython if clearing_files:
        test_cf_name = clearing_files[0].name
            test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 17:14:23,443 INFO ipython     print(f"\nTesting with: {test_cf_name}")
2025-09-19 17:14:23,443 INFO ipython     print(f"Current status: {test_cf.status}")
2025-09-19 17:14:23,443 INFO ipython     print(f"Attached documents:")
2025-09-19 17:14:23,443 INFO ipython     if test_cf.document:
            for doc in test_cf.document:
                        print(f"  - {doc.document_name}")
                        else:
                                print("  No documents attached")
2025-09-19 17:14:23,444 INFO ipython # Test properly
2025-09-19 17:14:23,444 INFO ipython test_cf_name = clearing_files[0].name
2025-09-19 17:14:23,444 INFO ipython test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 17:14:23,444 INFO ipython print(f"Testing with: {test_cf_name}")
2025-09-19 17:14:23,444 INFO ipython print(f"Current status: {test_cf.status}")
2025-09-19 17:14:23,444 INFO ipython print("Attached documents:")
2025-09-19 17:14:23,445 INFO ipython if test_cf.document:
        for doc in test_cf.document:
                    print(f"  - {doc.document_name}")
                    else:
                            print("  No documents attached")
2025-09-19 17:14:23,445 INFO ipython # Test the new function
2025-09-19 17:14:23,445 INFO ipython print("\nTesting transit document check...")
2025-09-19 17:14:23,445 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:14:23,445 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:14:23,445 INFO ipython # Check documents properly
2025-09-19 17:14:23,446 INFO ipython if test_cf.document:
        print("Documents found:")
            for doc in test_cf.document:
                        print(f"  - {doc.document_name}")
2025-09-19 17:14:23,446 INFO ipython else:
        print("No documents attached")
2025-09-19 17:14:23,446 INFO ipython # Let's manually add the required documents to test
2025-09-19 17:14:23,446 INFO ipython print("\nAdding test documents...")
2025-09-19 17:14:23,446 INFO ipython # Clear existing documents first
2025-09-19 17:14:23,446 INFO ipython test_cf.document = []
2025-09-19 17:14:23,447 INFO ipython # Add required transit documents
2025-09-19 17:14:23,447 INFO ipython required_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 17:14:23,447 INFO ipython for doc_name in required_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:14:23,447 INFO ipython print("Added documents:")
2025-09-19 17:14:23,447 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:14:23,447 INFO ipython # Test the function again
2025-09-19 17:14:23,448 INFO ipython print("\nTesting transit document check with all required docs...")
2025-09-19 17:14:23,448 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:14:23,448 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:14:23,448 INFO ipython === session end ===
2025-09-19 17:22:26,830 INFO ipython === bench console session ===
2025-09-19 17:22:26,830 INFO ipython === session end ===
2025-09-19 17:45:26,936 INFO ipython === bench console session ===
2025-09-19 17:45:26,937 INFO ipython import frappe
2025-09-19 17:45:26,938 INFO ipython # Test the Python-only implementation
2025-09-19 17:45:26,938 INFO ipython print("=== Testing Python-only Transit Document Status Update ===")
2025-09-19 17:45:26,938 INFO ipython # Get a test clearing file
2025-09-19 17:45:26,939 INFO ipython test_cf = frappe.get_doc("Clearing File", "CF-2025-0730")
2025-09-19 17:45:26,939 INFO ipython print(f"Original status: {test_cf.status}")
2025-09-19 17:45:26,940 INFO ipython # Test with Sea mode
2025-09-19 17:45:26,940 INFO ipython print("\n--- Testing Sea Mode ---")
2025-09-19 17:45:26,940 INFO ipython test_cf.mode_of_transport = "Sea"
2025-09-19 17:45:26,941 INFO ipython test_cf.status = "Draft"
2025-09-19 17:45:26,941 INFO ipython test_cf.document = []
2025-09-19 17:45:26,941 INFO ipython # Add required documents for Sea mode
2025-09-19 17:45:26,941 INFO ipython sea_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 17:45:26,942 INFO ipython for doc_name in sea_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:45:26,942 INFO ipython print(f"Mode: {test_cf.mode_of_transport}")
2025-09-19 17:45:26,942 INFO ipython print("Documents attached:")
2025-09-19 17:45:26,943 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:45:26,943 INFO ipython # Test the function directly
2025-09-19 17:45:26,943 INFO ipython print(f"Status before check: {test_cf.status}")
2025-09-19 17:45:26,944 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:45:26,944 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:45:26,944 INFO ipython # Test with Air mode
2025-09-19 17:45:26,944 INFO ipython print("\n--- Testing Air Mode ---")
2025-09-19 17:45:26,945 INFO ipython test_cf.mode_of_transport = "Air"
2025-09-19 17:45:26,945 INFO ipython test_cf.status = "Draft"
2025-09-19 17:45:26,945 INFO ipython test_cf.document = []
2025-09-19 17:45:26,946 INFO ipython # Add required documents for Air mode
2025-09-19 17:45:26,946 INFO ipython air_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 17:45:26,946 INFO ipython for doc_name in air_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:45:26,946 INFO ipython print(f"Mode: {test_cf.mode_of_transport}")
2025-09-19 17:45:26,946 INFO ipython print("Documents attached:")
2025-09-19 17:45:26,947 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:45:26,947 INFO ipython # Test the function directly
2025-09-19 17:45:26,947 INFO ipython print(f"Status before check: {test_cf.status}")
2025-09-19 17:45:26,947 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:45:26,948 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:45:26,948 INFO ipython print("\n=== Testing Complete ===")
2025-09-19 17:45:26,948 INFO ipython print("✅ Python-only implementation working correctly")
2025-09-19 17:45:26,948 INFO ipython # Test the before_save integration
2025-09-19 17:45:26,948 INFO ipython print("\n=== Testing before_save Integration ===")
2025-09-19 17:45:26,948 INFO ipython # Reset the test clearing file
2025-09-19 17:45:26,949 INFO ipython test_cf.mode_of_transport = "Sea"
2025-09-19 17:45:26,949 INFO ipython test_cf.status = "Draft"
2025-09-19 17:45:26,949 INFO ipython test_cf.document = []
2025-09-19 17:45:26,949 INFO ipython # Add required documents for Sea mode
2025-09-19 17:45:26,949 INFO ipython sea_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 17:45:26,949 INFO ipython for doc_name in sea_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:45:26,949 INFO ipython print(f"Status before save: {test_cf.status}")
2025-09-19 17:45:26,949 INFO ipython print("Documents attached:")
2025-09-19 17:45:26,950 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:45:26,950 INFO ipython # Test the before_save method which calls check_and_update_status
2025-09-19 17:45:26,950 INFO ipython test_cf.before_save()
2025-09-19 17:45:26,950 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 17:45:26,950 INFO ipython print("\n✅ before_save integration working correctly")
2025-09-19 17:45:26,950 INFO ipython print("The status will automatically update from Draft to Open when documents are attached and the form is saved.")
2025-09-19 17:45:26,950 INFO ipython === session end ===
2025-09-19 18:00:21,764 INFO ipython === bench console session ===
2025-09-19 18:00:21,765 INFO ipython import frappe
2025-09-19 18:00:21,765 INFO ipython # Let's debug step by step
2025-09-19 18:00:21,765 INFO ipython print("=== Debugging Transit Document Status Update ===")
2025-09-19 18:00:21,766 INFO ipython # Get a real clearing file
2025-09-19 18:00:21,766 INFO ipython clearing_files = frappe.get_all("Clearing File", fields=["name", "status", "mode_of_transport"], limit=3)
2025-09-19 18:00:21,766 INFO ipython print("Available clearing files:")
2025-09-19 18:00:21,767 INFO ipython for cf in clearing_files:
        print(f"  {cf.name}: Status={cf.status}, Mode={cf.mode_of_transport}")
        
2025-09-19 18:00:21,767 INFO ipython # Use the first one for testing
2025-09-19 18:00:21,768 INFO ipython if clearing_files:
        test_cf_name = clearing_files[0].name
            test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 18:00:21,769 INFO ipython     print(f"\nTesting with: {test_cf_name}")
2025-09-19 18:00:21,770 INFO ipython     print(f"Current status: {test_cf.status}")
2025-09-19 18:00:21,770 INFO ipython     print(f"Mode of transport: {test_cf.mode_of_transport}")
2025-09-19 18:00:21,770 INFO ipython     # Check current documents
2025-09-19 18:00:21,770 INFO ipython     print("Current documents:")
2025-09-19 18:00:21,771 INFO ipython     if test_cf.document:
            for doc in test_cf.document:
                        print(f"  - {doc.document_name}")
                        else:
                                print("  No documents attached")
2025-09-19 18:00:21,771 INFO ipython     # Let's test the function step by step
2025-09-19 18:00:21,771 INFO ipython     print("\n--- Testing check_transit_documents_and_update_status function ---")
2025-09-19 18:00:21,772 INFO ipython     # First, let's see what the function expects
2025-09-19 18:00:21,772 INFO ipython     base_required_docs = [
        "Authorisation Letter",
            "Commercial Invoice", 
                "Packing List"
                ]
2025-09-19 18:00:21,772 INFO ipython     if test_cf.mode_of_transport == "Air":
            required_docs = base_required_docs + ["Air Waybill (AWB)"]
            elif test_cf.mode_of_transport == "Sea":
                    required_docs = base_required_docs + ["Bill of Lading B/L"]
2025-09-19 18:00:21,773 INFO ipython     else:
            required_docs = base_required_docs + ["Air Waybill (AWB)"]
2025-09-19 18:00:21,773 INFO ipython     print(f"Required documents for {test_cf.mode_of_transport} mode:")
2025-09-19 18:00:21,773 INFO ipython     for doc in required_docs:
            print(f"  - {doc}")
                
2025-09-19 18:00:21,773 INFO ipython     # Check what's missing
2025-09-19 18:00:21,774 INFO ipython     attached_docs = []
2025-09-19 18:00:21,774 INFO ipython     if test_cf.document:
            attached_docs = [doc.document_name for doc in test_cf.document if doc.document_name]
            
2025-09-19 18:00:21,774 INFO ipython     missing_docs = []
2025-09-19 18:00:21,775 INFO ipython     for required_doc in required_docs:
            if required_doc not in attached_docs:
                        missing_docs.append(required_doc)
                        
2025-09-19 18:00:21,775 INFO ipython     print(f"\nMissing documents: {missing_docs}")
2025-09-19 18:00:21,775 INFO ipython     print(f"Current status: {test_cf.status}")
2025-09-19 18:00:21,776 INFO ipython     print(f"Status check condition: {test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,776 INFO ipython else:
        print("No clearing files found")
2025-09-19 18:00:21,776 INFO ipython # Debug properly
2025-09-19 18:00:21,777 INFO ipython test_cf_name = clearing_files[0].name
2025-09-19 18:00:21,777 INFO ipython test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 18:00:21,777 INFO ipython print(f"Testing with: {test_cf_name}")
2025-09-19 18:00:21,777 INFO ipython print(f"Current status: {test_cf.status}")
2025-09-19 18:00:21,778 INFO ipython print(f"Mode of transport: {test_cf.mode_of_transport}")
2025-09-19 18:00:21,778 INFO ipython # Check current documents
2025-09-19 18:00:21,778 INFO ipython print("Current documents:")
2025-09-19 18:00:21,778 INFO ipython if test_cf.document:
        for doc in test_cf.document:
                    print(f"  - {doc.document_name}")
                    else:
                            print("  No documents attached")
2025-09-19 18:00:21,779 INFO ipython # Let's manually add the required documents and test
2025-09-19 18:00:21,779 INFO ipython print("\n--- Adding required documents for testing ---")
2025-09-19 18:00:21,779 INFO ipython test_cf.document = []  # Clear existing
2025-09-19 18:00:21,780 INFO ipython # Add required documents based on mode
2025-09-19 18:00:21,780 INFO ipython if test_cf.mode_of_transport == "Sea":
        required_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
        else:
                required_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 18:00:21,780 INFO ipython for doc_name in required_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 18:00:21,780 INFO ipython print("Added documents:")
2025-09-19 18:00:21,781 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,781 INFO ipython # Test the function
2025-09-19 18:00:21,781 INFO ipython print(f"\nStatus before function call: {test_cf.status}")
2025-09-19 18:00:21,782 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 18:00:21,782 INFO ipython print(f"Status after function call: {test_cf.status}")
2025-09-19 18:00:21,782 INFO ipython # Test the before_save method
2025-09-19 18:00:21,782 INFO ipython print(f"\nTesting before_save method...")
2025-09-19 18:00:21,783 INFO ipython test_cf.status = "Draft"  # Reset to Draft
2025-09-19 18:00:21,783 INFO ipython print(f"Status before before_save: {test_cf.status}")
2025-09-19 18:00:21,783 INFO ipython test_cf.before_save()
2025-09-19 18:00:21,783 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 18:00:21,784 INFO ipython # Let's fix the syntax and add documents properly
2025-09-19 18:00:21,784 INFO ipython required_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 18:00:21,784 INFO ipython print("Adding documents for Sea mode:")
2025-09-19 18:00:21,784 INFO ipython for doc_name in required_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                                print(f"  Added: {doc_name}")
2025-09-19 18:00:21,785 INFO ipython print("\nDocuments now attached:")
2025-09-19 18:00:21,785 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,785 INFO ipython # Now test the function
2025-09-19 18:00:21,785 INFO ipython print(f"\nStatus before function call: {test_cf.status}")
2025-09-19 18:00:21,786 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 18:00:21,786 INFO ipython print(f"Status after function call: {test_cf.status}")
2025-09-19 18:00:21,786 INFO ipython # Let's also debug the function step by step
2025-09-19 18:00:21,786 INFO ipython print("\n--- Debugging the function logic ---")
2025-09-19 18:00:21,787 INFO ipython # Check what the function sees
2025-09-19 18:00:21,787 INFO ipython base_required_docs = [
    "Authorisation Letter",
        "Commercial Invoice",
            "Packing List"
            ]
2025-09-19 18:00:21,787 INFO ipython if test_cf.mode_of_transport == "Sea":
        required_transit_docs = base_required_docs + ["Bill of Lading B/L"]
        else:
                required_transit_docs = base_required_docs + ["Air Waybill (AWB)"]
2025-09-19 18:00:21,787 INFO ipython print(f"Required documents: {required_transit_docs}")
2025-09-19 18:00:21,787 INFO ipython # Get attached documents
2025-09-19 18:00:21,788 INFO ipython attached_docs = []
2025-09-19 18:00:21,788 INFO ipython if test_cf.document:
        attached_docs = [doc.document_name for doc in test_cf.document if doc.document_name]
        
2025-09-19 18:00:21,788 INFO ipython print(f"Attached documents: {attached_docs}")
2025-09-19 18:00:21,788 INFO ipython # Check missing documents
2025-09-19 18:00:21,789 INFO ipython missing_transit_docs = []
2025-09-19 18:00:21,789 INFO ipython for required_doc in required_transit_docs:
        if required_doc not in attached_docs:
                    missing_transit_docs.append(required_doc)
                    
2025-09-19 18:00:21,789 INFO ipython print(f"Missing documents: {missing_transit_docs}")
2025-09-19 18:00:21,790 INFO ipython print(f"All documents present: {len(missing_transit_docs) == 0}")
2025-09-19 18:00:21,790 INFO ipython print(f"Status is Draft: {test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,790 INFO ipython print(f"Should update status: {not missing_transit_docs and test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,790 INFO ipython # Let's add documents properly one by one
2025-09-19 18:00:21,790 INFO ipython print("Current document count:", len(test_cf.document))
2025-09-19 18:00:21,791 INFO ipython # Add documents manually
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Authorisation Letter", "document_type": "Authorisation Letter"})
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Commercial Invoice", "document_type": "Commercial Invoice"})
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Bill of Lading B/L", "document_type": "Bill of Lading B/L"})
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Packing List", "document_type": "Packing List"})
2025-09-19 18:00:21,791 INFO ipython print("Document count after adding:", len(test_cf.document))
2025-09-19 18:00:21,791 INFO ipython print("Documents attached:")
2025-09-19 18:00:21,792 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,792 INFO ipython # Now test the function again
2025-09-19 18:00:21,792 INFO ipython print(f"\nStatus before function call: {test_cf.status}")
2025-09-19 18:00:21,792 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 18:00:21,792 INFO ipython print(f"Status after function call: {test_cf.status}")
2025-09-19 18:00:21,792 INFO ipython # Let's also check what the function sees now
2025-09-19 18:00:21,792 INFO ipython attached_docs = [doc.document_name for doc in test_cf.document if doc.document_name]
2025-09-19 18:00:21,792 INFO ipython print(f"Function sees attached docs: {attached_docs}")
2025-09-19 18:00:21,793 INFO ipython required_transit_docs = ["Authorisation Letter", "Commercial Invoice", "Packing List", "Bill of Lading B/L"]
2025-09-19 18:00:21,793 INFO ipython missing_docs = [doc for doc in required_transit_docs if doc not in attached_docs]
2025-09-19 18:00:21,793 INFO ipython print(f"Missing docs: {missing_docs}")
2025-09-19 18:00:21,793 INFO ipython print(f"Should update: {len(missing_docs) == 0 and test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,793 INFO ipython # Test the real-world scenario - save the document
2025-09-19 18:00:21,793 INFO ipython print("=== Testing Real-World Save Scenario ===")
2025-09-19 18:00:21,793 INFO ipython # Reset the clearing file
2025-09-19 18:00:21,794 INFO ipython test_cf.status = "Draft"
2025-09-19 18:00:21,794 INFO ipython test_cf.document = []
2025-09-19 18:00:21,794 INFO ipython print(f"Initial status: {test_cf.status}")
2025-09-19 18:00:21,794 INFO ipython print(f"Initial document count: {len(test_cf.document)}")
2025-09-19 18:00:21,794 INFO ipython # Add the required documents for Sea mode
2025-09-19 18:00:21,794 INFO ipython test_cf.append("document", {"document_name": "Authorisation Letter", "document_type": "Authorisation Letter"})
2025-09-19 18:00:21,795 INFO ipython test_cf.append("document", {"document_name": "Commercial Invoice", "document_type": "Commercial Invoice"})
2025-09-19 18:00:21,795 INFO ipython test_cf.append("document", {"document_name": "Bill of Lading B/L", "document_type": "Bill of Lading B/L"})
2025-09-19 18:00:21,795 INFO ipython test_cf.append("document", {"document_name": "Packing List", "document_type": "Packing List"})
2025-09-19 18:00:21,795 INFO ipython print(f"Documents added: {len(test_cf.document)}")
2025-09-19 18:00:21,795 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,796 INFO ipython # Test the before_save method (this is what happens when user saves)
2025-09-19 18:00:21,796 INFO ipython print(f"\nStatus before save: {test_cf.status}")
2025-09-19 18:00:21,796 INFO ipython test_cf.before_save()
2025-09-19 18:00:21,796 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 18:00:21,797 INFO ipython # Now let's test with Air mode
2025-09-19 18:00:21,797 INFO ipython print("\n=== Testing Air Mode ===")
2025-09-19 18:00:21,797 INFO ipython test_cf.mode_of_transport = "Air"
2025-09-19 18:00:21,797 INFO ipython test_cf.status = "Draft"
2025-09-19 18:00:21,797 INFO ipython test_cf.document = []
2025-09-19 18:00:21,798 INFO ipython # Add required documents for Air mode
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Authorisation Letter", "document_type": "Authorisation Letter"})
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Commercial Invoice", "document_type": "Commercial Invoice"})
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Air Waybill (AWB)", "document_type": "Air Waybill (AWB)"})
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Packing List", "document_type": "Packing List"})
2025-09-19 18:00:21,799 INFO ipython print(f"Air mode documents added: {len(test_cf.document)}")
2025-09-19 18:00:21,799 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,799 INFO ipython print(f"Status before save: {test_cf.status}")
2025-09-19 18:00:21,799 INFO ipython test_cf.before_save()
2025-09-19 18:00:21,799 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 18:00:21,799 INFO ipython print("\n✅ Implementation is working correctly!")
2025-09-19 18:00:21,800 INFO ipython print("The status automatically changes from Draft to Open when all required documents are attached.")
2025-09-19 18:00:21,800 INFO ipython # Check what document types are available in the system
2025-09-19 18:00:21,800 INFO ipython print("=== Checking Available Document Types ===")
2025-09-19 18:00:21,800 INFO ipython # Get all clearing document types
2025-09-19 18:00:21,800 INFO ipython doc_types = frappe.get_all("Clearing Document Type", fields=["name", "linked_document"], order_by="name")
2025-09-19 18:00:21,800 INFO ipython print("Available Clearing Document Types:")
2025-09-19 18:00:21,800 INFO ipython for dt in doc_types:
        print(f"  - {dt.name} (linked to: {dt.linked_document})")
        
2025-09-19 18:00:21,801 INFO ipython # Check if our required documents exist
2025-09-19 18:00:21,801 INFO ipython required_sea_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 18:00:21,801 INFO ipython required_air_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 18:00:21,801 INFO ipython print(f"\nRequired Sea documents:")
2025-09-19 18:00:21,801 INFO ipython for doc in required_sea_docs:
        exists = any(dt.name == doc for dt in doc_types)
            print(f"  - {doc}: {'✅ EXISTS' if exists else '❌ MISSING'}")
2025-09-19 18:00:21,801 INFO ipython print(f"\nRequired Air documents:")
2025-09-19 18:00:21,801 INFO ipython for doc in required_air_docs:
        exists = any(dt.name == doc for dt in doc_types)
            print(f"  - {doc}: {'✅ EXISTS' if exists else '❌ MISSING'}")
2025-09-19 18:00:21,801 INFO ipython # Also check if there are any similar document names
2025-09-19 18:00:21,801 INFO ipython print(f"\nDocument types containing 'Bill':")
2025-09-19 18:00:21,802 INFO ipython bill_docs = [dt.name for dt in doc_types if 'bill' in dt.name.lower() or 'lading' in dt.name.lower()]
2025-09-19 18:00:21,802 INFO ipython for doc in bill_docs:
        print(f"  - {doc}")
        
2025-09-19 18:00:21,802 INFO ipython print(f"\nDocument types containing 'Waybill' or 'AWB':")
2025-09-19 18:00:21,802 INFO ipython awb_docs = [dt.name for dt in doc_types if 'waybill' in dt.name.lower() or 'awb' in dt.name.lower()]
2025-09-19 18:00:21,802 INFO ipython for doc in awb_docs:
        print(f"  - {doc}")
        
2025-09-19 18:00:21,802 INFO ipython === session end ===
2025-09-25 12:24:59,087 INFO ipython === bench console session ===
2025-09-25 12:24:59,088 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:24:59,088 INFO ipython print(f"Invoice: {invoice.name}")
2025-09-25 12:24:59,088 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:24:59,088 INFO ipython print(f"Outstanding Amount: {invoice.outstanding_amount}")
2025-09-25 12:24:59,088 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:24:59,088 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 12:24:59,089 INFO ipython print(f"Advance Paid: {invoice.advance_paid}")
2025-09-25 12:24:59,089 INFO ipython print(f"Total Advance: {invoice.total_advance}")
2025-09-25 12:24:59,089 INFO ipython print(f"Is Return: {invoice.is_return}")
2025-09-25 12:24:59,089 INFO ipython print(f"Return Against: {invoice.return_against}")
2025-09-25 12:24:59,089 INFO ipython print(f"Docstatus: {invoice.docstatus}")
2025-09-25 12:24:59,089 INFO ipython # Check payment entries for this invoice
2025-09-25 12:24:59,089 INFO ipython payment_entries = frappe.db.sql("""
    SELECT pe.name, pe.paid_amount, pe.received_amount, pe.difference_amount, 
               per.allocated_amount, per.outstanding_amount
                   FROM `tabPayment Entry` pe
                       JOIN `tabPayment Entry Reference` per ON pe.name = per.parent
                           WHERE per.reference_name = %s
                               ORDER BY pe.creation DESC
                               """, ("SINV-JS-43264",), as_dict=True)
2025-09-25 12:24:59,089 INFO ipython for pe in payment_entries:
        print(f"Payment Entry: {pe.name}")
            print(f"  Paid Amount: {pe.paid_amount}")
2025-09-25 12:24:59,089 INFO ipython     print(f"  Received Amount: {pe.received_amount}")
2025-09-25 12:24:59,089 INFO ipython     print(f"  Difference Amount: {pe.difference_amount}")
2025-09-25 12:24:59,090 INFO ipython     print(f"  Allocated Amount: {pe.allocated_amount}")
2025-09-25 12:24:59,090 INFO ipython     print(f"  Outstanding Amount: {pe.outstanding_amount}")
2025-09-25 12:24:59,090 INFO ipython     print("---")
2025-09-25 12:24:59,090 INFO ipython for pe in payment_entries:
        print(f"Payment Entry: {pe.name}")
            print(f"  Paid Amount: {pe.paid_amount}")
2025-09-25 12:24:59,090 INFO ipython     print(f"  Received Amount: {pe.received_amount}")
2025-09-25 12:24:59,090 INFO ipython     print(f"  Difference Amount: {pe.difference_amount}")
2025-09-25 12:24:59,090 INFO ipython     print(f"  Allocated Amount: {pe.allocated_amount}")
2025-09-25 12:24:59,090 INFO ipython     print(f"  Outstanding Amount: {pe.outstanding_amount}")
2025-09-25 12:24:59,090 INFO ipython     print("---")
2025-09-25 12:24:59,090 INFO ipython print("Payment entries found:", len(payment_entries))
2025-09-25 12:24:59,090 INFO ipython if payment_entries:
        pe = payment_entries[0]
            print("First payment entry details:")
2025-09-25 12:24:59,091 INFO ipython     for key, value in pe.items():
            print(f"  {key}: {value}")
        # Check if this is a POS invoice
    print(f"Is POS: {invoice.is_pos}")
2025-09-25 12:24:59,091 INFO ipython print(f"POS Profile: {invoice.pos_profile}")
2025-09-25 12:24:59,091 INFO ipython # Check for POS Invoice Merge Log
2025-09-25 12:24:59,091 INFO ipython pos_merge_logs = frappe.db.sql("""
    SELECT piml.name, piml.posting_date, piml.posting_time, 
               pimli.pos_invoice, pimli.customer, pimli.grand_total
                   FROM `tabPOS Invoice Merge Log` piml
                       JOIN `tabPOS Invoice Merge Log Item` pimli ON piml.name = pimli.parent
                           WHERE pimli.pos_invoice = %s
                           """, ("SINV-JS-43264",), as_dict=True)
2025-09-25 12:24:59,091 INFO ipython print(f"POS Merge Log entries: {len(pos_merge_logs)}")
2025-09-25 12:24:59,091 INFO ipython for log in pos_merge_logs:
        print(f"Merge Log: {log.name}")
            print(f"  POS Invoice: {log.pos_invoice}")
2025-09-25 12:24:59,091 INFO ipython     print(f"  Grand Total: {log.grand_total}")
2025-09-25 12:24:59,091 INFO ipython     print("---")
2025-09-25 12:24:59,091 INFO ipython # Check POS invoice payments
2025-09-25 12:24:59,091 INFO ipython print(f"Is POS: {invoice.is_pos}")
2025-09-25 12:24:59,091 INFO ipython if hasattr(invoice, 'payments'):
        print(f"Number of payments: {len(invoice.payments)}")
            total_paid = 0
2025-09-25 12:24:59,092 INFO ipython     for payment in invoice.payments:
            print(f"Payment Mode: {payment.mode_of_payment}")
                print(f"  Amount: {payment.amount}")
2025-09-25 12:24:59,092 INFO ipython         print(f"  Base Amount: {payment.base_amount}")
2025-09-25 12:24:59,092 INFO ipython         total_paid += payment.amount
2025-09-25 12:24:59,092 INFO ipython         print("---")
2025-09-25 12:24:59,092 INFO ipython     print(f"Total paid from payments table: {total_paid}")
2025-09-25 12:24:59,092 INFO ipython else:
        print("No payments attribute found")
2025-09-25 12:24:59,092 INFO ipython === session end ===
2025-09-25 12:30:45,523 INFO ipython === bench console session ===
2025-09-25 12:30:45,523 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:30:45,523 INFO ipython print(f"Invoice: {invoice.name}")
2025-09-25 12:30:45,524 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:30:45,524 INFO ipython print(f"Outstanding Amount: {invoice.outstanding_amount}")
2025-09-25 12:30:45,524 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:30:45,524 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 12:30:45,524 INFO ipython print(f"Is POS: {invoice.is_pos}")
2025-09-25 12:30:45,524 INFO ipython print(f"POS Profile: {invoice.pos_profile}")
2025-09-25 12:30:45,524 INFO ipython # Check payments
2025-09-25 12:30:45,525 INFO ipython if hasattr(invoice, 'payments') and invoice.payments:
        print(f"\nPayments ({len(invoice.payments)}):")
            total_paid = 0
2025-09-25 12:30:45,525 INFO ipython     for payment in invoice.payments:
            print(f"  {payment.mode_of_payment}: {payment.amount}")
                total_paid += payment.amount
2025-09-25 12:30:45,525 INFO ipython     print(f"Total from payments: {total_paid}")
2025-09-25 12:30:45,525 INFO ipython else:
        print("No payments found")
2025-09-25 12:30:45,525 INFO ipython print("Checking payments...")
2025-09-25 12:30:45,525 INFO ipython print(f"Has payments attribute: {hasattr(invoice, 'payments')}")
2025-09-25 12:30:45,525 INFO ipython if hasattr(invoice, 'payments'):
        print(f"Number of payments: {len(invoice.payments)}")
            for i, payment in enumerate(invoice.payments):
                        print(f"Payment {i+1}: {payment.mode_of_payment} = {payment.amount}")
2025-09-25 12:30:45,526 INFO ipython len(invoice.payments)
2025-09-25 12:30:45,526 INFO ipython payment = invoice.payments[0]
2025-09-25 12:30:45,526 INFO ipython print(f"Mode of Payment: {payment.mode_of_payment}")
2025-09-25 12:30:45,526 INFO ipython print(f"Amount: {payment.amount}")
2025-09-25 12:30:45,526 INFO ipython print(f"Base Amount: {payment.base_amount}")
2025-09-25 12:30:45,526 INFO ipython print(f"Type: {payment.type}")
2025-09-25 12:30:45,526 INFO ipython print(f"Account: {payment.account}")
2025-09-25 12:30:45,526 INFO ipython # Get POS Profile details
2025-09-25 12:30:45,526 INFO ipython pos_profile = frappe.get_doc("POS Profile", "Tina Patel")
2025-09-25 12:30:45,526 INFO ipython print(f"POS Profile: {pos_profile.name}")
2025-09-25 12:30:45,527 INFO ipython print(f"Currency: {pos_profile.currency}")
2025-09-25 12:30:45,527 INFO ipython print(f"Write Off Account: {pos_profile.write_off_account}")
2025-09-25 12:30:45,527 INFO ipython print(f"Write Off Cost Center: {pos_profile.write_off_cost_center}")
2025-09-25 12:30:45,527 INFO ipython print(f"Write Off Limit: {pos_profile.write_off_limit}")
2025-09-25 12:30:45,527 INFO ipython # Check invoice items for rounding
2025-09-25 12:30:45,527 INFO ipython print(f"\nInvoice Items:")
2025-09-25 12:30:45,527 INFO ipython total_amount = 0
2025-09-25 12:30:45,527 INFO ipython for item in invoice.items:
        print(f"Item: {item.item_code}")
            print(f"  Qty: {item.qty}")
2025-09-25 12:30:45,528 INFO ipython     print(f"  Rate: {item.rate}")
2025-09-25 12:30:45,528 INFO ipython     print(f"  Amount: {item.amount}")
2025-09-25 12:30:45,528 INFO ipython     total_amount += item.amount
2025-09-25 12:30:45,528 INFO ipython print(f"Total from items: {total_amount}")
2025-09-25 12:30:45,528 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:30:45,528 INFO ipython print(f"Difference: {invoice.grand_total - total_amount}")
2025-09-25 12:30:45,528 INFO ipython print(f"Number of items: {len(invoice.items)}")
2025-09-25 12:30:45,528 INFO ipython item = invoice.items[0]
2025-09-25 12:30:45,529 INFO ipython print(f"Item: {item.item_code}")
2025-09-25 12:30:45,529 INFO ipython print(f"Qty: {item.qty}")
2025-09-25 12:30:45,529 INFO ipython print(f"Rate: {item.rate}")
2025-09-25 12:30:45,529 INFO ipython print(f"Amount: {item.amount}")
2025-09-25 12:30:45,529 INFO ipython # Check taxes
2025-09-25 12:30:45,529 INFO ipython print(f"\nTaxes:")
2025-09-25 12:30:45,529 INFO ipython print(f"Total taxes and charges: {invoice.total_taxes_and_charges}")
