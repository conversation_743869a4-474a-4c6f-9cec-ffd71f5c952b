2025-09-25 12:30:45,529 INFO ipython print(f"Net total: {invoice.net_total}")
2025-09-25 12:30:45,531 INFO ipython print(f"Grand total: {invoice.grand_total}")
2025-09-25 12:30:45,531 INFO ipython # Check if there are any rounding adjustments
2025-09-25 12:30:45,531 INFO ipython print(f"\nRounding adjustment: {getattr(invoice, 'rounding_adjustment', 'N/A')}")
2025-09-25 12:30:45,531 INFO ipython print(f"Rounded total: {getattr(invoice, 'rounded_total', 'N/A')}")
2025-09-25 12:30:45,531 INFO ipython total_items = 0
2025-09-25 12:30:45,531 INFO ipython for i, item in enumerate(invoice.items):
        print(f"Item {i+1}: {item.item_code}")
            print(f"  Qty: {item.qty}, Rate: {item.rate}, Amount: {item.amount}")
2025-09-25 12:30:45,531 INFO ipython     total_items += item.amount
2025-09-25 12:30:45,531 INFO ipython print(f"\nTotal from items: {total_items}")
2025-09-25 12:30:45,532 INFO ipython print(f"Net total: {invoice.net_total}")
2025-09-25 12:30:45,532 INFO ipython print(f"Total taxes: {invoice.total_taxes_and_charges}")
2025-09-25 12:30:45,532 INFO ipython print(f"Grand total: {invoice.grand_total}")
2025-09-25 12:30:45,532 INFO ipython print(f"Calculation: {invoice.net_total} + {invoice.total_taxes_and_charges} = {invoice.net_total + invoice.total_taxes_and_charges}")
2025-09-25 12:30:45,532 INFO ipython # Check the exact difference
2025-09-25 12:30:45,532 INFO ipython difference = invoice.grand_total - (invoice.net_total + invoice.total_taxes_and_charges)
2025-09-25 12:30:45,532 INFO ipython print(f"Difference: {difference}")
2025-09-25 12:30:45,532 INFO ipython print("All items:")
2025-09-25 12:30:45,533 INFO ipython total_items = 0
2025-09-25 12:30:45,533 INFO ipython for i in range(len(invoice.items)):
        item = invoice.items[i]
            print(f"Item {i+1}: {item.item_code} - Qty: {item.qty}, Rate: {item.rate}, Amount: {item.amount}")
2025-09-25 12:30:45,533 INFO ipython     total_items += item.amount
2025-09-25 12:30:45,533 INFO ipython print(f"Total from all items: {total_items}")
2025-09-25 12:30:45,533 INFO ipython # Now let's check why the payment is 0.01 less
2025-09-25 12:30:45,533 INFO ipython print(f"\nPayment Analysis:")
2025-09-25 12:30:45,533 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:30:45,533 INFO ipython print(f"Payment Amount: {payment.amount}")
2025-09-25 12:30:45,534 INFO ipython print(f"Difference: {invoice.grand_total - payment.amount}")
2025-09-25 12:30:45,534 INFO ipython # Check if there's any write-off or adjustment
2025-09-25 12:30:45,534 INFO ipython print(f"\nWrite-off details:")
2025-09-25 12:30:45,534 INFO ipython print(f"Write-off amount: {getattr(invoice, 'write_off_amount', 'N/A')}")
2025-09-25 12:30:45,534 INFO ipython print(f"Change amount: {getattr(invoice, 'change_amount', 'N/A')}")
2025-09-25 12:30:45,534 INFO ipython print(f"Outstanding amount: {invoice.outstanding_amount}")
2025-09-25 12:30:45,534 INFO ipython print("Checking each item individually:")
2025-09-25 12:30:45,534 INFO ipython print(f"Item 1: {invoice.items[0].item_code} - Amount: {invoice.items[0].amount}")
2025-09-25 12:30:45,535 INFO ipython print(f"Item 2: {invoice.items[1].item_code} - Amount: {invoice.items[1].amount}")
2025-09-25 12:30:45,535 INFO ipython print(f"Item 3: {invoice.items[2].item_code} - Amount: {invoice.items[2].amount}")
2025-09-25 12:30:45,535 INFO ipython total = invoice.items[0].amount + invoice.items[1].amount + invoice.items[2].amount
2025-09-25 12:30:45,535 INFO ipython print(f"Manual total: {total}")
2025-09-25 12:30:45,535 INFO ipython print(f"Net total from invoice: {invoice.net_total}")
2025-09-25 12:30:45,535 INFO ipython # Check if there are any discounts
2025-09-25 12:30:45,535 INFO ipython print(f"\nDiscount details:")
2025-09-25 12:30:45,535 INFO ipython print(f"Discount amount: {getattr(invoice, 'discount_amount', 'N/A')}")
2025-09-25 12:30:45,536 INFO ipython print(f"Additional discount percentage: {getattr(invoice, 'additional_discount_percentage', 'N/A')}")
2025-09-25 12:30:45,536 INFO ipython print(f"Apply discount on: {getattr(invoice, 'apply_discount_on', 'N/A')}")
2025-09-25 12:30:45,536 INFO ipython # Check taxes in detail
2025-09-25 12:30:45,536 INFO ipython print("Tax details:")
2025-09-25 12:30:45,536 INFO ipython if hasattr(invoice, 'taxes') and invoice.taxes:
        for i, tax in enumerate(invoice.taxes):
                    print(f"Tax {i+1}: {tax.description}")
                            print(f"  Rate: {tax.rate}%")
2025-09-25 12:30:45,536 INFO ipython         print(f"  Tax Amount: {tax.tax_amount}")
2025-09-25 12:30:45,536 INFO ipython         print(f"  Total: {tax.total}")
2025-09-25 12:30:45,536 INFO ipython         print("---")
2025-09-25 12:30:45,536 INFO ipython else:
        print("No taxes found")
2025-09-25 12:30:45,537 INFO ipython # Check the calculation flow
2025-09-25 12:30:45,537 INFO ipython print(f"\nCalculation flow:")
2025-09-25 12:30:45,537 INFO ipython print(f"Items total: {total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Net total: {invoice.net_total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Difference (discount?): {total - invoice.net_total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Taxes: {invoice.total_taxes_and_charges}")
2025-09-25 12:30:45,537 INFO ipython print(f"Grand total: {invoice.grand_total}")
2025-09-25 12:30:45,537 INFO ipython print(f"Payment: {payment.amount}")
2025-09-25 12:30:45,537 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:30:45,538 INFO ipython # Check if this is a rounding issue in the payment calculation
2025-09-25 12:30:45,538 INFO ipython from frappe.utils import flt
2025-09-25 12:30:45,538 INFO ipython print(f"\nRounding check:")
2025-09-25 12:30:45,538 INFO ipython print(f"Grand total (flt): {flt(invoice.grand_total, 2)}")
2025-09-25 12:30:45,538 INFO ipython print(f"Payment (flt): {flt(payment.amount, 2)}")
2025-09-25 12:30:45,538 INFO ipython print(f"Difference (flt): {flt(invoice.grand_total - payment.amount, 2)}")
2025-09-25 12:30:45,538 INFO ipython print(f"Number of taxes: {len(invoice.taxes)}")
2025-09-25 12:30:45,538 INFO ipython if invoice.taxes:
        tax = invoice.taxes[0]
            print(f"Tax: {tax.description}")
2025-09-25 12:30:45,538 INFO ipython     print(f"Rate: {tax.rate}%")
2025-09-25 12:30:45,539 INFO ipython     print(f"Tax Amount: {tax.tax_amount}")
2025-09-25 12:30:45,539 INFO ipython # The issue is clear now:
2025-09-25 12:30:45,539 INFO ipython # Items total: 217,600.00
2025-09-25 12:30:45,539 INFO ipython # But the system calculates: Net total (184,406.78) + Taxes (33,193.22) = 217,600.00
2025-09-25 12:30:45,539 INFO ipython # This means the tax is calculated as INCLUSIVE, reducing the net total
2025-09-25 12:30:45,539 INFO ipython # But the payment was made for 217,599.99 instead of 217,600.00
2025-09-25 12:30:45,539 INFO ipython print(f"\nSUMMARY:")
2025-09-25 12:30:45,539 INFO ipython print(f"The issue is a 0.01 rounding difference in the payment amount.")
2025-09-25 12:30:45,539 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:30:45,540 INFO ipython print(f"Payment: {payment.amount}")
2025-09-25 12:30:45,540 INFO ipython print(f"This creates an outstanding of 0.01 causing 'Partly Paid' status.")
2025-09-25 12:30:45,540 INFO ipython # Check if we can fix this with write-off
2025-09-25 12:30:45,540 INFO ipython print(f"\nPOS Profile write-off limit: {pos_profile.write_off_limit}")
2025-09-25 12:30:45,540 INFO ipython print(f"Current outstanding: {invoice.outstanding_amount}")
2025-09-25 12:30:45,540 INFO ipython print(f"Can be written off: {invoice.outstanding_amount <= pos_profile.write_off_limit}")
2025-09-25 12:30:45,540 INFO ipython === session end ===
2025-09-25 12:35:50,371 INFO ipython === bench console session ===
2025-09-25 12:35:50,371 INFO ipython # Fix the invoice by writing off the 0.01 outstanding amount
2025-09-25 12:35:50,371 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,372 INFO ipython print(f"Before fix:")
2025-09-25 12:35:50,372 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,372 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,372 INFO ipython print(f"Write-off amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,372 INFO ipython # Set write-off amount to the outstanding amount
2025-09-25 12:35:50,372 INFO ipython invoice.write_off_amount = invoice.outstanding_amount
2025-09-25 12:35:50,372 INFO ipython invoice.write_off_account = "Write Off - MPASL"  # From POS Profile
2025-09-25 12:35:50,372 INFO ipython invoice.write_off_cost_center = "Main - MPASL"   # From POS Profile
2025-09-25 12:35:50,372 INFO ipython # Save the changes
2025-09-25 12:35:50,372 INFO ipython invoice.save()
2025-09-25 12:35:50,373 INFO ipython print(f"\nAfter fix:")
2025-09-25 12:35:50,373 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,373 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,373 INFO ipython print(f"Write-off amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,373 INFO ipython # Since the invoice is submitted, we need to use db_set to update the fields
2025-09-25 12:35:50,373 INFO ipython # This bypasses the validation but should be used carefully
2025-09-25 12:35:50,373 INFO ipython # First, let's check if we can use the automatic write-off flag
2025-09-25 12:35:50,373 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,373 INFO ipython # Update the write-off fields directly in the database
2025-09-25 12:35:50,373 INFO ipython frappe.db.set_value("Sales Invoice", "SINV-JS-43264", {
    "write_off_amount": 0.01,
        "write_off_account": "Write Off - MPASL",
            "write_off_cost_center": "Main - MPASL",
                "write_off_outstanding_amount_automatically": 1
                })
2025-09-25 12:35:50,373 INFO ipython # Recalculate the outstanding amount and status
2025-09-25 12:35:50,374 INFO ipython invoice.reload()
2025-09-25 12:35:50,374 INFO ipython from erpnext.controllers.taxes_and_totals import calculate_taxes_and_totals
2025-09-25 12:35:50,374 INFO ipython invoice.calculate_outstanding_amount()
2025-09-25 12:35:50,374 INFO ipython invoice.set_status(update=True)
2025-09-25 12:35:50,374 INFO ipython print(f"After database update:")
2025-09-25 12:35:50,374 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,374 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,374 INFO ipython print(f"Write-off amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,374 INFO ipython # Commit the changes
2025-09-25 12:35:50,374 INFO ipython frappe.db.commit()
2025-09-25 12:35:50,374 INFO ipython # Let's manually recalculate the outstanding amount
2025-09-25 12:35:50,374 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,375 INFO ipython print(f"Current values:")
2025-09-25 12:35:50,375 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:35:50,375 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 12:35:50,375 INFO ipython print(f"Write-off Amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,375 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,375 INFO ipython # Calculate the correct outstanding amount
2025-09-25 12:35:50,375 INFO ipython # Outstanding = Grand Total - Paid Amount - Write-off Amount
2025-09-25 12:35:50,375 INFO ipython new_outstanding = invoice.grand_total - invoice.paid_amount - invoice.write_off_amount
2025-09-25 12:35:50,375 INFO ipython print(f"Calculated Outstanding: {new_outstanding}")
2025-09-25 12:35:50,375 INFO ipython # Update the outstanding amount directly
2025-09-25 12:35:50,375 INFO ipython frappe.db.set_value("Sales Invoice", "SINV-JS-43264", "outstanding_amount", new_outstanding)
2025-09-25 12:35:50,375 INFO ipython # Reload and check status
2025-09-25 12:35:50,376 INFO ipython invoice.reload()
2025-09-25 12:35:50,376 INFO ipython invoice.set_status(update=True)
2025-09-25 12:35:50,376 INFO ipython print(f"\nAfter recalculation:")
2025-09-25 12:35:50,376 INFO ipython print(f"Outstanding: {invoice.outstanding_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,376 INFO ipython frappe.db.commit()
2025-09-25 12:35:50,376 INFO ipython # Final verification
2025-09-25 12:35:50,376 INFO ipython invoice = frappe.get_doc("Sales Invoice", "SINV-JS-43264")
2025-09-25 12:35:50,376 INFO ipython print("=== FINAL STATUS ===")
2025-09-25 12:35:50,376 INFO ipython print(f"Invoice: {invoice.name}")
2025-09-25 12:35:50,376 INFO ipython print(f"Status: {invoice.status}")
2025-09-25 12:35:50,376 INFO ipython print(f"Grand Total: {invoice.grand_total}")
2025-09-25 12:35:50,376 INFO ipython print(f"Paid Amount: {invoice.paid_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Write-off Amount: {invoice.write_off_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Outstanding Amount: {invoice.outstanding_amount}")
2025-09-25 12:35:50,376 INFO ipython print(f"Write-off Account: {invoice.write_off_account}")
2025-09-25 12:35:50,377 INFO ipython print(f"Write-off Cost Center: {invoice.write_off_cost_center}")
2025-09-25 12:35:50,377 INFO ipython print("===================")
2025-09-25 12:35:50,377 INFO ipython === session end ===
