2025-08-14 12:49:38,785 INFO ipython optimized_css = """/* ===================== GLOBAL PRINT STYLES ===================== */
.print-format .letter-head {
    display: none;
    }
    
    /* Reset default spacing and alignment */
    .print-format {
        padding: 15px !important;
            font-family: 'Calibri', Arial, sans-serif;
                font-size: 11px;
                    line-height: 1.3;
                        color: #000;
                            max-width: 100%;
                            }
                            
                            .print-format td, .print-format th {
                                vertical-align: top !important;
                                    padding: 3px 5px !important;
                                    }
                                    
                                    .print-format th {
                                        color: black !important;
                                            font-weight: bold;
                                                border-bottom-width: 1px !important;
                                                }
                                                
                                                .print-format p {
                                                    margin: 0px 0px 4px !important;
                                                    }
                                                    
                                                    /* Remove default ERPNext spacing */
                                                    .frappe-control, .form-section {
                                                        margin-bottom: 0 !important;
                                                        }
                                                        
                                                        /* ===================== RESPONSIVE LAYOUT ===================== */
                                                        html, body {
                                                            height: auto !important;
                                                                overflow: visible !important;
                                                                }
                                                                
                                                                .print-format {
                                                                    display: block;
                                                                        height: auto;
                                                                            overflow: visible;
                                                                            }
                                                                            
                                                                            /* ===================== PAGE BREAK HANDLING ===================== */
                                                                            @media print {
                                                                                @page {
                                                                                        margin: 8mm 6mm;
                                                                                                size: A4;
                                                                                                    }
                                                                                                        
                                                                                                            .print-format {
                                                                                                                    padding: 6px !important;
                                                                                                                            font-size: 10px;
                                                                                                                                }
                                                                                                                                    
                                                                                                                                        /* Prevent page breaks inside important sections */
                                                                                                                                            .header-section, .customer-details, .supplier-details, .order-details {
                                                                                                                                                    page-break-inside: avoid;
                                                                                                                                                        }
                                                                                                                                                            
                                                                                                                                                                /* Allow page breaks between item rows if needed */
                                                                                                                                                                    .items-table tbody tr {
                                                                                                                                                                            page-break-inside: avoid;
                                                                                                                                                                                    page-break-after: auto;
                                                                                                                                                                                        }
                                                                                                                                                                                            
                                                                                                                                                                                                /* Keep footer sections together */
                                                                                                                                                                                                    .footer-section {
                                                                                                                                                                                                            page-break-inside: avoid;
                                                                                                                                                                                                                    margin-top: 10px;
                                                                                                                                                                                                                        }
                                                                                                                                                                                                                            
                                                                                                                                                                                                                                /* Optimize spacing for print */
                                                                                                                                                                                                                                    .responsive-margin {
                                                                                                                                                                                                                                            margin-top: 8px !important;
                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                
                                                                                                                                                                                                                                                /* ===================== DYNAMIC CONTENT HANDLING ===================== */
                                                                                                                                                                                                                                                .content-wrapper {
                                                                                                                                                                                                                                                    display: flex;
                                                                                                                                                                                                                                                        flex-direction: column;
                                                                                                                                                                                                                                                            min-height: auto;
                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                            .items-section {
                                                                                                                                                                                                                                                                flex: 1;
                                                                                                                                                                                                                                                                    margin: 12px 0;
                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                    .footer-section {
                                                                                                                                                                                                                                                                        margin-top: auto;
                                                                                                                                                                                                                                                                            padding-top: 12px;
                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                            /* ===================== ALIGNMENT FIXES ===================== */
                                                                                                                                                                                                                                                                            .text-left { text-align: left !important; }
                                                                                                                                                                                                                                                                            .text-center { text-align: center !important; }
                                                                                                                                                                                                                                                                            .text-right { text-align: right !important; }
                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                            /* ===================== TABLE IMPROVEMENTS ===================== */
                                                                                                                                                                                                                                                                            .items-table {
                                                                                                                                                                                                                                                                                width: 100%;
                                                                                                                                                                                                                                                                                    border-collapse: collapse;
                                                                                                                                                                                                                                                                                        margin: 12px 0;
                                                                                                                                                                                                                                                                                            font-size: 11px;
                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                            .items-table th {
                                                                                                                                                                                                                                                                                                border-top: 1px solid #000;
                                                                                                                                                                                                                                                                                                    border-bottom: 1px solid #000;
                                                                                                                                                                                                                                                                                                        padding: 4px 6px;
                                                                                                                                                                                                                                                                                                            font-weight: bold;
                                                                                                                                                                                                                                                                                                                background-color: #f8f9fa;
                                                                                                                                                                                                                                                                                                                }
                                                                                                                                                                                                                                                                                                                
                                                                                                                                                                                                                                                                                                                .items-table td {
                                                                                                                                                                                                                                                                                                                    border-bottom: 1px solid #ddd;
                                                                                                                                                                                                                                                                                                                        padding: 3px 6px;
                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                        /* Handle many items by reducing row height */
                                                                                                                                                                                                                                                                                                                        .items-table.many-items {
                                                                                                                                                                                                                                                                                                                            font-size: 10px;
                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                            .items-table.many-items td,
                                                                                                                                                                                                                                                                                                                            .items-table.many-items th {
                                                                                                                                                                                                                                                                                                                                padding: 2px 4px;
                                                                                                                                                                                                                                                                                                                                    line-height: 1.1;
                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                    /* ===================== RESPONSIVE SPACING ===================== */
                                                                                                                                                                                                                                                                                                                                    .responsive-margin {
                                                                                                                                                                                                                                                                                                                                        margin-top: 12px;
                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                                        @media print {
                                                                                                                                                                                                                                                                                                                                            .responsive-margin {
                                                                                                                                                                                                                                                                                                                                                    margin-top: 6px;
                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                        }
                                                                                                                                                                                                                                                                                                                                                        
                                                                                                                                                                                                                                                                                                                                                        /* ===================== FOOTER LAYOUT ===================== */
                                                                                                                                                                                                                                                                                                                                                        .footer-layout {
                                                                                                                                                                                                                                                                                                                                                            display: flex;
                                                                                                                                                                                                                                                                                                                                                                justify-content: space-between;
                                                                                                                                                                                                                                                                                                                                                                    align-items: flex-start;
                                                                                                                                                                                                                                                                                                                                                                        margin-top: 15px;
                                                                                                                                                                                                                                                                                                                                                                            gap: 20px;
                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                                                                            .footer-left {
                                                                                                                                                                                                                                                                                                                                                                                flex: 1;
                                                                                                                                                                                                                                                                                                                                                                                    max-width: 45%;
                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                                                                    .footer-right {
                                                                                                                                                                                                                                                                                                                                                                                        flex: 1;
                                                                                                                                                                                                                                                                                                                                                                                            max-width: 45%;
                                                                                                                                                                                                                                                                                                                                                                                            }
                                                                                                                                                                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                                                                                                                                            @media print {
                                                                                                                                                                                                                                                                                                                                                                                                .footer-layout {
                                                                                                                                                                                                                                                                                                                                                                                                        margin-top: 8px;
                                                                                                                                                                                                                                                                                                                                                                                                                gap: 10px;
                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                    }
                                                                                                                                                                                                                                                                                                                                                                                                                    
                                                                                                                                                                                                                                                                                                                                                                                                                    """
2025-08-14 12:49:38,791 INFO ipython print("CSS defined successfully!")
2025-08-14 12:49:38,791 INFO ipython # Update Rubis Invoice
2025-08-14 12:49:38,791 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Invoice")
2025-08-14 12:49:38,792 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,792 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,792 INFO ipython doc.save()
2025-08-14 12:49:38,792 INFO ipython print("✅ Rubis Invoice updated!")
2025-08-14 12:49:38,792 INFO ipython # Update Rubis Sales Order
2025-08-14 12:49:38,793 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Sales Order")
2025-08-14 12:49:38,793 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,793 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,793 INFO ipython doc.save()
2025-08-14 12:49:38,793 INFO ipython print("✅ Rubis Sales Order updated!")
2025-08-14 12:49:38,794 INFO ipython # Update Rubis Purchase Order
2025-08-14 12:49:38,794 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Purchase Order")
2025-08-14 12:49:38,794 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,794 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,794 INFO ipython doc.save()
2025-08-14 12:49:38,794 INFO ipython print("✅ Rubis Purchase Order updated!")
2025-08-14 12:49:38,795 INFO ipython # Update Rubis Purchase Receipt
2025-08-14 12:49:38,795 INFO ipython doc = frappe.get_doc("Print Format", "Rubis Purchase Receipt")
2025-08-14 12:49:38,795 INFO ipython doc.css = optimized_css
2025-08-14 12:49:38,795 INFO ipython doc.font_size = 11
2025-08-14 12:49:38,795 INFO ipython doc.save()
2025-08-14 12:49:38,796 INFO ipython print("✅ Rubis Purchase Receipt updated!")
2025-08-14 12:49:38,796 INFO ipython # Commit all changes
2025-08-14 12:49:38,796 INFO ipython frappe.db.commit()
2025-08-14 12:49:38,796 INFO ipython print("🎉 All print formats updated successfully in your rubis site!")
2025-08-14 12:49:38,796 INFO ipython === session end ===
2025-08-18 09:08:00,866 INFO ipython === bench console session ===
2025-08-18 09:08:00,866 INFO ipython from csf_tz.patches.setup_biometrics_system import validate_biometrics_setup
2025-08-18 09:08:00,867 INFO ipython issues = validate_biometrics_setup()
2025-08-18 09:08:00,867 INFO ipython print("Validation Issues:", issues)
2025-08-18 09:08:00,867 INFO ipython from frappe.utils import getdate
2025-08-18 09:08:00,867 INFO ipython print("Available utils:", dir(frappe.utils)[:10])
2025-08-18 09:08:00,867 INFO ipython [x for x in dir(frappe.utils) if 'year' in x.lower()]
2025-08-18 09:08:00,867 INFO ipython === session end ===
2025-08-18 09:14:01,656 INFO ipython === bench console session ===
2025-08-18 09:14:01,656 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:14:01,656 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:14:01,656 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:14:01,656 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:14:01,656 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:14:01,657 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:14:01,657 INFO ipython from datetime import date
2025-08-18 09:14:01,658 INFO ipython import calendar
2025-08-18 09:14:01,658 INFO ipython april_7_2024 = date(2024, 4, 7)
2025-08-18 09:14:01,658 INFO ipython print("April 7, 2024 is a:", calendar.day_name[april_7_2024.weekday()])
2025-08-18 09:14:01,658 INFO ipython === session end ===
2025-08-18 09:24:47,729 INFO ipython === bench console session ===
2025-08-18 09:24:47,730 INFO ipython from csf_tz.utils.tanzania_holidays import create_tanzania_holiday_list
2025-08-18 09:24:47,730 INFO ipython holiday_list = create_tanzania_holiday_list(2024)
2025-08-18 09:24:47,730 INFO ipython print("Created holiday list:", holiday_list)
2025-08-18 09:24:47,730 INFO ipython from csf_tz.utils.tanzania_holidays import get_saturday_overtime_hours
2025-08-18 09:24:47,730 INFO ipython from datetime import date
2025-08-18 09:24:47,730 INFO ipython # Test Saturday overtime calculation
2025-08-18 09:24:47,730 INFO ipython saturday_date = date(2024, 4, 6)  # A Saturday
2025-08-18 09:24:47,730 INFO ipython saturday_ot = get_saturday_overtime_hours("12:00:00", "17:00:00", saturday_date)
2025-08-18 09:24:47,730 INFO ipython print(f"Saturday overtime for 12:00-17:00: {saturday_ot}")
2025-08-18 09:24:47,731 INFO ipython # Test non-Saturday
2025-08-18 09:24:47,731 INFO ipython monday_date = date(2024, 4, 8)  # A Monday
2025-08-18 09:24:47,731 INFO ipython monday_ot = get_saturday_overtime_hours("12:00:00", "17:00:00", monday_date)
2025-08-18 09:24:47,731 INFO ipython print(f"Monday overtime (should be 0): {monday_ot}")
2025-08-18 09:24:47,731 INFO ipython from csf_tz.patches.setup_biometrics_system import validate_biometrics_setup
2025-08-18 09:24:47,731 INFO ipython issues = validate_biometrics_setup()
2025-08-18 09:24:47,731 INFO ipython print("Validation Issues:", issues)
2025-08-18 09:24:47,731 INFO ipython from csf_tz.patches.setup_biometrics_system import apply_custom_fields
2025-08-18 09:24:47,731 INFO ipython apply_custom_fields()
2025-08-18 09:24:47,732 INFO ipython print("Custom fields applied")
2025-08-18 09:24:47,732 INFO ipython from csf_tz.patches.setup_biometrics_system import execute
2025-08-18 09:24:47,732 INFO ipython execute()
2025-08-18 09:24:47,732 INFO ipython print("Full setup completed")
2025-08-18 09:24:47,732 INFO ipython === session end ===
2025-08-21 10:03:58,991 INFO ipython === bench console session ===
2025-08-21 10:03:58,991 INFO ipython import frappe
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee OT Component", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee Education", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee External Work History", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee Internal Work History", force=True)
2025-08-21 10:03:58,992 INFO ipython === session end ===
2025-08-28 10:59:42,766 INFO ipython === bench console session ===
2025-08-28 10:59:42,767 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 10:59:42,767 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 10:59:42,767 INFO ipython columns, data = execute(filters)
2025-08-28 10:59:42,768 INFO ipython print("Report executed successfully!")
2025-08-28 10:59:42,768 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 10:59:42,768 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 10:59:42,768 INFO ipython frappe.db.describe("Sales Order Item")
2025-08-28 10:59:42,768 INFO ipython === session end ===
2025-08-28 11:03:59,882 INFO ipython === bench console session ===
2025-08-28 11:03:59,882 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 11:03:59,883 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 11:03:59,883 INFO ipython columns, data = execute(filters)
2025-08-28 11:03:59,883 INFO ipython print("Report executed successfully!")
2025-08-28 11:03:59,883 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 11:03:59,884 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython bench --site rubis console
2025-08-28 11:03:59,884 INFO ipython === session end ===
2025-08-28 11:06:58,629 INFO ipython === bench console session ===
2025-08-28 11:06:58,629 INFO ipython from csf_tz.csf_tz.report.sales_cycle_report.sales_cycle_report import execute
2025-08-28 11:06:58,629 INFO ipython filters = {"from_date": "2025-08-01", "to_date": "2025-08-28"}
2025-08-28 11:06:58,630 INFO ipython columns, data = execute(filters)
2025-08-28 11:06:58,630 INFO ipython print("Report executed successfully!")
2025-08-28 11:06:58,630 INFO ipython print(f"Number of columns: {len(columns)}")
2025-08-28 11:06:58,630 INFO ipython print(f"Number of data rows: {len(data)}")
2025-08-28 11:06:58,630 INFO ipython bench --site rubis console
2025-08-28 11:06:58,630 INFO ipython === session end ===
2025-09-04 17:29:08,223 INFO ipython === bench console session ===
2025-09-04 17:29:08,235 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site explore console
2025-09-04 17:29:08,235 INFO ipython frappe.get_meta("Gate Pass").get_field("workflow_state")
2025-09-04 17:29:08,236 INFO ipython frappe.db.get_list("DocType", filters={"name": ["like", "%Gate%"]}, fields=["name"])
2025-09-04 17:29:08,236 INFO ipython === session end ===
2025-09-10 17:44:51,668 INFO ipython === bench console session ===
2025-09-10 17:44:51,669 INFO ipython bench --site explore console --no-startup
2025-09-10 17:44:51,669 INFO ipython === session end ===
2025-09-10 18:04:43,094 INFO ipython === bench console session ===
2025-09-10 18:04:43,422 INFO ipython gate_passes = frappe.get_all("Gate Pass", limit=5)
2025-09-10 18:04:43,423 INFO ipython print(f"Found {len(gate_passes)} gate passes")
2025-09-10 18:04:43,424 INFO ipython from frappe import get_hooks
2025-09-10 18:04:43,424 INFO ipython hooks = get_hooks("scheduler_events")
2025-09-10 18:04:43,425 INFO ipython print("Scheduler events:", hooks)
2025-09-10 18:04:43,425 INFO ipython from icd_tz.icd_tz.doctype.gate_pass.gate_pass import auto_expire_gate_passes
2025-09-10 18:04:43,425 INFO ipython try:
        result = auto_expire_gate_passes()
            print("Auto expire function executed successfully")
2025-09-10 18:04:43,426 INFO ipython     print(f"Result: {result}")
2025-09-10 18:04:43,426 INFO ipython except Exception as e:
        print(f"Error: {e}")
2025-09-10 18:04:43,426 INFO ipython try:
        result = auto_expire_gate_passes()
            print("Auto expire function executed successfully")
2025-09-10 18:04:43,427 INFO ipython     print(f"Result: {result}")
2025-09-10 18:04:43,427 INFO ipython except Exception as e:
        print(f"Error: {e}")
2025-09-10 18:04:43,428 INFO ipython result = auto_expire_gate_passes()
2025-09-10 18:04:43,428 INFO ipython print("Function executed successfully")
2025-09-10 18:04:43,428 INFO ipython print("Function completed")
2025-09-10 18:04:43,429 INFO ipython === session end ===
2025-09-15 22:11:20,084 INFO ipython === bench console session ===
2025-09-15 22:11:20,084 INFO ipython # Test the API methods
2025-09-15 22:11:20,085 INFO ipython import frappe
2025-09-15 22:11:20,085 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, sync_detection_system
2025-09-15 22:11:20,085 INFO ipython # Test get_scheduled_broadcasts
2025-09-15 22:11:20,085 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 22:11:20,085 INFO ipython print("get_scheduled_broadcasts result:", result)
2025-09-15 22:11:20,085 INFO ipython # Test sync_detection_system
2025-09-15 22:11:20,085 INFO ipython sync_result = sync_detection_system()
2025-09-15 22:11:20,085 INFO ipython print("sync_detection_system result:", sync_result)
2025-09-15 22:11:20,085 INFO ipython # Test task functions
2025-09-15 22:11:20,086 INFO ipython from broadcast.broadcast.tasks import mark_missed_advertisements, generate_daily_report
2025-09-15 22:11:20,086 INFO ipython # Test mark_missed_advertisements
2025-09-15 22:11:20,086 INFO ipython mark_missed_advertisements()
2025-09-15 22:11:20,086 INFO ipython print("mark_missed_advertisements completed")
2025-09-15 22:11:20,086 INFO ipython # Test generate_daily_report
2025-09-15 22:11:20,086 INFO ipython generate_daily_report()
2025-09-15 22:11:20,086 INFO ipython print("generate_daily_report completed")
2025-09-15 22:11:20,086 INFO ipython === session end ===
2025-09-15 22:14:15,682 INFO ipython === bench console session ===
2025-09-15 22:14:15,682 INFO ipython # Quick test of the broadcast app
2025-09-15 22:14:15,682 INFO ipython import frappe
2025-09-15 22:14:15,682 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, sync_detection_system
2025-09-15 22:14:15,682 INFO ipython # Test API methods
2025-09-15 22:14:15,682 INFO ipython print("Testing get_scheduled_broadcasts...")
2025-09-15 22:14:15,682 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 22:14:15,682 INFO ipython print("Result:", result)
2025-09-15 22:14:15,683 INFO ipython print("\nTesting sync_detection_system...")
2025-09-15 22:14:15,683 INFO ipython sync_result = sync_detection_system()
2025-09-15 22:14:15,683 INFO ipython print("Sync result:", sync_result)
2025-09-15 22:14:15,683 INFO ipython # Check app statistics
2025-09-15 22:14:15,683 INFO ipython print("\nApp Statistics:")
2025-09-15 22:14:15,683 INFO ipython total_ads = frappe.db.count("Advertisement Broadcast")
2025-09-15 22:14:15,683 INFO ipython print(f"Total Advertisements: {total_ads}")
2025-09-15 22:14:15,683 INFO ipython # Test task functions
2025-09-15 22:14:15,683 INFO ipython print("\nTesting scheduled tasks...")
2025-09-15 22:14:15,683 INFO ipython from broadcast.broadcast.tasks import mark_missed_advertisements, generate_daily_report
2025-09-15 22:14:15,684 INFO ipython mark_missed_advertisements()
2025-09-15 22:14:15,684 INFO ipython print("mark_missed_advertisements completed")
2025-09-15 22:14:15,684 INFO ipython generate_daily_report()
2025-09-15 22:14:15,684 INFO ipython print("generate_daily_report completed")
2025-09-15 22:14:15,684 INFO ipython print("\n✅ All tests passed! Broadcast app is working perfectly!")
2025-09-15 22:14:15,684 INFO ipython === session end ===
2025-09-15 23:12:57,340 INFO ipython === bench console session ===
2025-09-15 23:12:57,340 INFO ipython exec(open('test_broadcast_fixes.py').read())
2025-09-15 23:12:57,341 INFO ipython # Test the API methods
2025-09-15 23:12:57,341 INFO ipython from broadcast.broadcast.api import get_scheduled_broadcasts, manual_broadcast_log
2025-09-15 23:12:57,341 INFO ipython result = get_scheduled_broadcasts(hours_ahead=24)
2025-09-15 23:12:57,341 INFO ipython print("API Test:", result['status'])
2025-09-15 23:12:57,341 INFO ipython # Test creating advertisement broadcast
2025-09-15 23:12:57,341 INFO ipython from frappe.utils import now_datetime, add_to_date
2025-09-15 23:12:57,341 INFO ipython test_ad = frappe.get_doc({
    "doctype": "Advertisement Broadcast",
        "advertisement_title": "Test Advertisement - Functionality Check",
            "customer": "Test Customer",
                "presenter": "<EMAIL>",
                    "scheduled_date": add_to_date(now_datetime(), days=1).date(),
                        "scheduled_time": "10:00:00",
                            "duration_seconds": 30,
                                "rate_per_second": 10.0,
                                    "status": "Scheduled",
                                        "auto_generate_invoice": 1,
                                            "notification_sent": 0
                                            })
2025-09-15 23:12:57,342 INFO ipython test_ad.insert(ignore_permissions=True)
2025-09-15 23:12:57,342 INFO ipython print("Advertisement created:", test_ad.name)
2025-09-15 23:12:57,342 INFO ipython print("auto_generate_invoice:", test_ad.auto_generate_invoice)
2025-09-15 23:12:57,342 INFO ipython print("notification_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython # Test notification functionality
2025-09-15 23:12:57,342 INFO ipython test_ad.mark_notification_sent()
2025-09-15 23:12:57,342 INFO ipython print("After mark_notification_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython test_ad.mark_notification_not_sent()
2025-09-15 23:12:57,342 INFO ipython print("After mark_notification_not_sent:", test_ad.notification_sent)
2025-09-15 23:12:57,342 INFO ipython # Test manual broadcast log API
2025-09-15 23:12:57,343 INFO ipython try:
        log_result = manual_broadcast_log(
                advertisement_id="ADB-2025-00002",
                        actual_datetime=now_datetime(),
                                actual_duration=30,
                                        notes="Test broadcast log"
                                            )
                                                print("Manual broadcast log result:", log_result['status'])
2025-09-15 23:12:57,343 INFO ipython except Exception as e:
        print("Manual broadcast log error:", str(e))
2025-09-15 23:12:57,343 INFO ipython try:
        log_result = manual_broadcast_log(
                advertisement_id="ADB-2025-00002",
                        actual_datetime=now_datetime(),
                                actual_duration=30,
                                        notes="Test broadcast log"
                                            )
                                                print("Manual broadcast log result:", log_result['status'])
2025-09-15 23:12:57,343 INFO ipython except Exception as e:
        print("Manual broadcast log error:", str(e))
2025-09-15 23:12:57,343 INFO ipython === session end ===
2025-09-15 23:18:53,979 INFO ipython === bench console session ===
2025-09-15 23:18:53,980 INFO ipython # Test the service item creation
2025-09-15 23:18:53,980 INFO ipython from broadcast.broadcast.doctype.advertisement_broadcast.advertisement_broadcast import AdvertisementBroadcast
2025-09-15 23:18:53,980 INFO ipython test_doc = AdvertisementBroadcast()
2025-09-15 23:18:53,980 INFO ipython service_item = test_doc.get_or_create_service_item()
2025-09-15 23:18:53,980 INFO ipython print("Service item created/found:", service_item)
2025-09-15 23:18:53,980 INFO ipython # Test the service item creation correctly
2025-09-15 23:18:53,980 INFO ipython test_doc = frappe.get_doc("Advertisement Broadcast")
2025-09-15 23:18:53,981 INFO ipython service_item = test_doc.get_or_create_service_item()
2025-09-15 23:18:53,981 INFO ipython print("Service item created/found:", service_item)
2025-09-15 23:18:53,981 INFO ipython # Test the service item creation with new doc
2025-09-15 23:18:53,981 INFO ipython test_doc = frappe.new_doc("Advertisement Broadcast")
2025-09-15 23:18:53,981 INFO ipython service_item = test_doc.get_or_create_service_item()
2025-09-15 23:18:53,981 INFO ipython print("Service item created/found:", service_item)
2025-09-15 23:18:53,981 INFO ipython === session end ===
2025-09-17 15:22:02,217 INFO ipython === bench console session ===
2025-09-17 15:22:02,219 INFO ipython frappe.db.get_list("Dashboard", fields=["name", "dashboard_name"])
2025-09-17 15:22:02,219 INFO ipython frappe.get_installed_apps()
2025-09-17 15:22:02,219 INFO ipython === session end ===
2025-09-17 15:32:08,140 INFO ipython === bench console session ===
2025-09-17 15:32:08,141 INFO ipython exec(open('apps/sdg_reporting/sdg_reporting/sdg_reporting/create_simple_dashboards.py').read())
2025-09-17 15:32:08,142 INFO ipython import os; os.getcwd()
2025-09-17 15:32:08,142 INFO ipython exec(open('../apps/sdg_reporting/sdg_reporting/sdg_reporting/create_simple_dashboards.py').read())
2025-09-17 15:32:08,142 INFO ipython create_simple_dashboards()
2025-09-17 15:32:08,142 INFO ipython import frappe
2025-09-17 15:32:08,142 INFO ipython import json
2025-09-17 15:32:08,142 INFO ipython # Create Number Cards
2025-09-17 15:32:08,142 INFO ipython cards = [
    {
            "name": "Total SDG Goals",
                    "label": "Total SDG Goals",
                            "type": "Document Type",
                                    "document_type": "SDG Goal",
                                            "function": "Count",
                                                    "color": "#2E86AB",
                                                            "is_public": 1,
                                                                    "module": "Sdg Reporting",
                                                                        },
                                                                            {
                                                                                    "name": "Active Sustainability Metrics",
                                                                                            "label": "Active Sustainability Metrics",
                                                                                                    "type": "Document Type",
                                                                                                            "document_type": "Sustainability Metric",
                                                                                                                    "function": "Count",
                                                                                                                            "color": "#A23B72",
                                                                                                                                    "is_public": 1,
                                                                                                                                            "module": "Sdg Reporting",
                                                                                                                                                },
                                                                                                                                                ]
2025-09-17 15:32:08,143 INFO ipython for card_data in cards:
        if not frappe.db.exists("Number Card", card_data["name"]):
                    try:
                                    doc = frappe.new_doc("Number Card")
                                                doc.update(card_data)
2025-09-17 15:32:08,143 INFO ipython             doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,143 INFO ipython             print(f"Created Number Card: {card_data['name']}")
2025-09-17 15:32:08,143 INFO ipython         except Exception as e:
                print(f"Error creating Number Card {card_data['name']}: {str(e)}")
2025-09-17 15:32:08,143 INFO ipython # Create a simple number card
2025-09-17 15:32:08,143 INFO ipython doc = frappe.new_doc("Number Card")
2025-09-17 15:32:08,143 INFO ipython doc.name = "Total SDG Goals"
2025-09-17 15:32:08,143 INFO ipython doc.label = "Total SDG Goals"
2025-09-17 15:32:08,143 INFO ipython doc.type = "Document Type"
2025-09-17 15:32:08,144 INFO ipython doc.document_type = "SDG Goal"
2025-09-17 15:32:08,144 INFO ipython doc.function = "Count"
2025-09-17 15:32:08,144 INFO ipython doc.color = "#2E86AB"
2025-09-17 15:32:08,144 INFO ipython doc.is_public = 1
2025-09-17 15:32:08,144 INFO ipython doc.module = "Sdg Reporting"
2025-09-17 15:32:08,144 INFO ipython doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,144 INFO ipython print("Created Number Card: Total SDG Goals")
2025-09-17 15:32:08,144 INFO ipython # Create a simple dashboard chart
2025-09-17 15:32:08,144 INFO ipython chart = frappe.new_doc("Dashboard Chart")
2025-09-17 15:32:08,144 INFO ipython chart.name = "SDG Metrics Count"
2025-09-17 15:32:08,144 INFO ipython chart.chart_name = "SDG Metrics Count"
2025-09-17 15:32:08,145 INFO ipython chart.chart_type = "Count"
2025-09-17 15:32:08,145 INFO ipython chart.document_type = "Sustainability Metric"
2025-09-17 15:32:08,145 INFO ipython chart.based_on = "creation"
2025-09-17 15:32:08,145 INFO ipython chart.timeseries = 1
2025-09-17 15:32:08,145 INFO ipython chart.time_interval = "Monthly"
2025-09-17 15:32:08,145 INFO ipython chart.timespan = "Last Year"
2025-09-17 15:32:08,145 INFO ipython chart.type = "Line"
2025-09-17 15:32:08,145 INFO ipython chart.color = "#2E86AB"
2025-09-17 15:32:08,146 INFO ipython chart.is_public = 1
2025-09-17 15:32:08,146 INFO ipython chart.module = "Sdg Reporting"
2025-09-17 15:32:08,146 INFO ipython chart.insert(ignore_permissions=True)
2025-09-17 15:32:08,146 INFO ipython print("Created Dashboard Chart: SDG Metrics Count")
2025-09-17 15:32:08,146 INFO ipython # Create a simple dashboard chart with filters_json
2025-09-17 15:32:08,146 INFO ipython chart = frappe.new_doc("Dashboard Chart")
2025-09-17 15:32:08,146 INFO ipython chart.name = "SDG Metrics Count"
2025-09-17 15:32:08,146 INFO ipython chart.chart_name = "SDG Metrics Count"
2025-09-17 15:32:08,147 INFO ipython chart.chart_type = "Count"
2025-09-17 15:32:08,147 INFO ipython chart.document_type = "Sustainability Metric"
2025-09-17 15:32:08,147 INFO ipython chart.based_on = "creation"
2025-09-17 15:32:08,147 INFO ipython chart.timeseries = 1
2025-09-17 15:32:08,147 INFO ipython chart.time_interval = "Monthly"
2025-09-17 15:32:08,147 INFO ipython chart.timespan = "Last Year"
2025-09-17 15:32:08,147 INFO ipython chart.type = "Line"
2025-09-17 15:32:08,147 INFO ipython chart.color = "#2E86AB"
2025-09-17 15:32:08,147 INFO ipython chart.is_public = 1
2025-09-17 15:32:08,148 INFO ipython chart.module = "Sdg Reporting"
2025-09-17 15:32:08,148 INFO ipython chart.filters_json = "[]"
2025-09-17 15:32:08,148 INFO ipython chart.insert(ignore_permissions=True)
2025-09-17 15:32:08,148 INFO ipython print("Created Dashboard Chart: SDG Metrics Count")
2025-09-17 15:32:08,148 INFO ipython # Create the dashboard
2025-09-17 15:32:08,148 INFO ipython dashboard = frappe.new_doc("Dashboard")
2025-09-17 15:32:08,148 INFO ipython dashboard.name = "SDG Overview"
2025-09-17 15:32:08,149 INFO ipython dashboard.dashboard_name = "SDG Overview"
2025-09-17 15:32:08,149 INFO ipython dashboard.module = "Sdg Reporting"
2025-09-17 15:32:08,149 INFO ipython dashboard.is_standard = 1
2025-09-17 15:32:08,149 INFO ipython # Add the chart
2025-09-17 15:32:08,149 INFO ipython dashboard.append("charts", {
    "chart": "SDG Metrics Count",
        "width": "Full"
        })
2025-09-17 15:32:08,149 INFO ipython # Add the card
2025-09-17 15:32:08,149 INFO ipython dashboard.append("cards", {
    "card": "Total SDG Goals"
    })
2025-09-17 15:32:08,149 INFO ipython dashboard.insert(ignore_permissions=True)
2025-09-17 15:32:08,149 INFO ipython print("Created Dashboard: SDG Overview")
2025-09-17 15:32:08,150 INFO ipython # Set chart as standard
2025-09-17 15:32:08,150 INFO ipython chart_doc = frappe.get_doc("Dashboard Chart", "SDG Metrics Count")
2025-09-17 15:32:08,150 INFO ipython chart_doc.is_standard = 1
2025-09-17 15:32:08,150 INFO ipython chart_doc.save(ignore_permissions=True)
2025-09-17 15:32:08,150 INFO ipython # Set card as standard
2025-09-17 15:32:08,150 INFO ipython card_doc = frappe.get_doc("Number Card", "Total SDG Goals")
2025-09-17 15:32:08,150 INFO ipython card_doc.is_standard = 1
2025-09-17 15:32:08,150 INFO ipython card_doc.save(ignore_permissions=True)
2025-09-17 15:32:08,151 INFO ipython print("Set chart and card as standard")
2025-09-17 15:32:08,151 INFO ipython # Create the dashboard again
2025-09-17 15:32:08,151 INFO ipython dashboard = frappe.new_doc("Dashboard")
2025-09-17 15:32:08,151 INFO ipython dashboard.name = "SDG Overview"
2025-09-17 15:32:08,151 INFO ipython dashboard.dashboard_name = "SDG Overview"
2025-09-17 15:32:08,151 INFO ipython dashboard.module = "Sdg Reporting"
2025-09-17 15:32:08,151 INFO ipython dashboard.is_standard = 1
2025-09-17 15:32:08,151 INFO ipython # Add the chart
2025-09-17 15:32:08,151 INFO ipython dashboard.append("charts", {
    "chart": "SDG Metrics Count",
        "width": "Full"
        })
2025-09-17 15:32:08,152 INFO ipython # Add the card
2025-09-17 15:32:08,152 INFO ipython dashboard.append("cards", {
    "card": "Total SDG Goals"
    })
2025-09-17 15:32:08,152 INFO ipython dashboard.insert(ignore_permissions=True)
2025-09-17 15:32:08,152 INFO ipython print("Created Dashboard: SDG Overview")
2025-09-17 15:32:08,152 INFO ipython # Create sample SDG Goals
2025-09-17 15:32:08,152 INFO ipython sample_goals = [
    {"goal_no": "1", "goal_name": "No Poverty", "description": "End poverty in all its forms everywhere"},
        {"goal_no": "2", "goal_name": "Zero Hunger", "description": "End hunger, achieve food security and improved nutrition"},
            {"goal_no": "3", "goal_name": "Good Health and Well-being", "description": "Ensure healthy lives and promote well-being for all"},
            ]
2025-09-17 15:32:08,152 INFO ipython for goal_data in sample_goals:
        if not frappe.db.exists("SDG Goal", goal_data["goal_no"]):
                    doc = frappe.new_doc("SDG Goal")
                            doc.update(goal_data)
2025-09-17 15:32:08,152 INFO ipython         doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,153 INFO ipython         print(f"Created SDG Goal: {goal_data['goal_no']}")
2025-09-17 15:32:08,153 INFO ipython # Create sample Sustainability Metrics
2025-09-17 15:32:08,153 INFO ipython sample_metrics = [
    {
            "metric_name": "Maternal Mortality Rate",
                    "esg_pillar": "Social",
                            "sdg_link": "3",
                                    "unit": "per 100,000 live births",
                                            "measures": "Health outcomes for mothers"
                                                },
                                                    {
                                                            "metric_name": "Healthcare Coverage",
                                                                    "esg_pillar": "Social", 
                                                                            "sdg_link": "3",
                                                                                    "unit": "percentage",
                                                                                            "measures": "Population with health insurance"
                                                                                                },
                                                                                                ]
2025-09-17 15:32:08,153 INFO ipython for metric_data in sample_metrics:
        if not frappe.db.exists("Sustainability Metric", metric_data["metric_name"]):
                    doc = frappe.new_doc("Sustainability Metric")
                            doc.update(metric_data)
2025-09-17 15:32:08,154 INFO ipython         doc.insert(ignore_permissions=True)
2025-09-17 15:32:08,154 INFO ipython         print(f"Created Sustainability Metric: {metric_data['metric_name']}")
2025-09-17 15:32:08,154 INFO ipython frappe.db.commit()
2025-09-17 15:32:08,154 INFO ipython print("Sample data created and committed!")
2025-09-17 15:32:08,154 INFO ipython frappe.db.get_list("Dashboard", fields=["name", "dashboard_name"], filters={"module": "Sdg Reporting"})
2025-09-17 15:32:08,154 INFO ipython === session end ===
2025-09-17 18:47:02,208 INFO ipython === bench console session ===
2025-09-17 18:47:02,209 INFO ipython exec(open('apps/sdg_reporting/sdg_reporting/sdg_reporting/create_report_based_dashboards.py').read())
2025-09-17 18:47:02,209 INFO ipython import os
2025-09-17 18:47:02,209 INFO ipython print(os.getcwd())
2025-09-17 18:47:02,209 INFO ipython print(os.path.exists('apps/sdg_reporting/sdg_reporting/sdg_reporting/create_report_based_dashboards.py'))
2025-09-17 18:47:02,210 INFO ipython exec(open('../apps/sdg_reporting/sdg_reporting/sdg_reporting/create_report_based_dashboards.py').read())
2025-09-17 18:47:02,210 INFO ipython # Create the reports first
2025-09-17 18:47:02,210 INFO ipython import frappe
2025-09-17 18:47:02,210 INFO ipython # Create SDG Metrics Mapping Report
2025-09-17 18:47:02,210 INFO ipython if not frappe.db.exists("Report", "SDG Metrics Mapping"):
        report = frappe.new_doc("Report")
            report.name = "SDG Metrics Mapping"
2025-09-17 18:47:02,210 INFO ipython     report.report_name = "SDG Metrics Mapping"
2025-09-17 18:47:02,210 INFO ipython     report.report_type = "Script Report"
2025-09-17 18:47:02,210 INFO ipython     report.ref_doctype = "Sustainability Metric"
2025-09-17 18:47:02,211 INFO ipython     report.module = "Sdg Reporting"
2025-09-17 18:47:02,211 INFO ipython     report.is_standard = "Yes"
2025-09-17 18:47:02,211 INFO ipython     report.insert(ignore_permissions=True)
2025-09-17 18:47:02,211 INFO ipython     print("✅ Created Report: SDG Metrics Mapping")
2025-09-17 18:47:02,211 INFO ipython # Create SDG Progress Tracking Report
2025-09-17 18:47:02,211 INFO ipython if not frappe.db.exists("Report", "SDG Progress Tracking"):
        report = frappe.new_doc("Report")
            report.name = "SDG Progress Tracking"
2025-09-17 18:47:02,211 INFO ipython     report.report_name = "SDG Progress Tracking"
2025-09-17 18:47:02,211 INFO ipython     report.report_type = "Script Report"
2025-09-17 18:47:02,211 INFO ipython     report.ref_doctype = "Sustainability Metric"
2025-09-17 18:47:02,212 INFO ipython     report.module = "Sdg Reporting"
2025-09-17 18:47:02,212 INFO ipython     report.is_standard = "Yes"
2025-09-17 18:47:02,212 INFO ipython     report.insert(ignore_permissions=True)
2025-09-17 18:47:02,212 INFO ipython     print("✅ Created Report: SDG Progress Tracking")
2025-09-17 18:47:02,212 INFO ipython # Create SDG Goal Distribution Report
2025-09-17 18:47:02,212 INFO ipython if not frappe.db.exists("Report", "SDG Goal Distribution"):
        report = frappe.new_doc("Report")
            report.name = "SDG Goal Distribution"
2025-09-17 18:47:02,212 INFO ipython     report.report_name = "SDG Goal Distribution"
2025-09-17 18:47:02,212 INFO ipython     report.report_type = "Script Report"
2025-09-17 18:47:02,212 INFO ipython     report.ref_doctype = "Sustainability Entry"
2025-09-17 18:47:02,212 INFO ipython     report.module = "Sdg Reporting"
2025-09-17 18:47:02,213 INFO ipython     report.is_standard = "Yes"
2025-09-17 18:47:02,213 INFO ipython     report.insert(ignore_permissions=True)
2025-09-17 18:47:02,213 INFO ipython     print("✅ Created Report: SDG Goal Distribution")
2025-09-17 18:47:02,214 INFO ipython print("Reports created successfully!")
2025-09-17 18:47:02,214 INFO ipython # Create SDG Metrics Mapping Report properly
2025-09-17 18:47:02,214 INFO ipython if not frappe.db.exists("Report", "SDG Metrics Mapping"):
        report = frappe.new_doc("Report")
            report.name = "SDG Metrics Mapping"
2025-09-17 18:47:02,214 INFO ipython     report.report_name = "SDG Metrics Mapping"
2025-09-17 18:47:02,215 INFO ipython     report.report_type = "Script Report"
2025-09-17 18:47:02,215 INFO ipython     report.ref_doctype = "Sustainability Metric"
2025-09-17 18:47:02,215 INFO ipython     report.module = "Sdg Reporting"
2025-09-17 18:47:02,215 INFO ipython     report.is_standard = "Yes"
2025-09-17 18:47:02,215 INFO ipython     report.insert(ignore_permissions=True)
2025-09-17 18:47:02,215 INFO ipython     print("✅ Created Report: SDG Metrics Mapping")
2025-09-17 18:47:02,215 INFO ipython else:
        print("Report 'SDG Metrics Mapping' already exists")
2025-09-17 18:47:02,215 INFO ipython report = frappe.new_doc("Report")
2025-09-17 18:47:02,215 INFO ipython report.name = "SDG Metrics Mapping"
2025-09-17 18:47:02,216 INFO ipython report.report_name = "SDG Metrics Mapping"
2025-09-17 18:47:02,216 INFO ipython report.report_type = "Script Report"
2025-09-17 18:47:02,216 INFO ipython report.ref_doctype = "Sustainability Metric"
2025-09-17 18:47:02,216 INFO ipython report.module = "Sdg Reporting"
2025-09-17 18:47:02,216 INFO ipython report.is_standard = "Yes"
2025-09-17 18:47:02,216 INFO ipython report.insert(ignore_permissions=True)
2025-09-17 18:47:02,216 INFO ipython print("✅ Created Report: SDG Metrics Mapping")
2025-09-17 18:47:02,216 INFO ipython report2 = frappe.new_doc("Report")
2025-09-17 18:47:02,216 INFO ipython report2.name = "SDG Progress Tracking"
2025-09-17 18:47:02,217 INFO ipython report2.report_name = "SDG Progress Tracking"
2025-09-17 18:47:02,217 INFO ipython report2.report_type = "Script Report"
2025-09-17 18:47:02,217 INFO ipython report2.ref_doctype = "Sustainability Metric"
2025-09-17 18:47:02,217 INFO ipython report2.module = "Sdg Reporting"
2025-09-17 18:47:02,217 INFO ipython report2.is_standard = "Yes"
2025-09-17 18:47:02,217 INFO ipython report2.insert(ignore_permissions=True)
2025-09-17 18:47:02,217 INFO ipython print("✅ Created Report: SDG Progress Tracking")
2025-09-17 18:47:02,217 INFO ipython report3 = frappe.new_doc("Report")
2025-09-17 18:47:02,217 INFO ipython report3.name = "SDG Goal Distribution"
2025-09-17 18:47:02,218 INFO ipython report3.report_name = "SDG Goal Distribution"
2025-09-17 18:47:02,218 INFO ipython report3.report_type = "Script Report"
2025-09-17 18:47:02,218 INFO ipython report3.ref_doctype = "Sustainability Entry"
2025-09-17 18:47:02,218 INFO ipython report3.module = "Sdg Reporting"
2025-09-17 18:47:02,218 INFO ipython report3.is_standard = "Yes"
2025-09-17 18:47:02,218 INFO ipython report3.insert(ignore_permissions=True)
2025-09-17 18:47:02,218 INFO ipython print("✅ Created Report: SDG Goal Distribution")
2025-09-17 18:47:02,218 INFO ipython # Create Dashboard Chart for SDG Metrics Mapping
2025-09-17 18:47:02,219 INFO ipython chart1 = frappe.new_doc("Dashboard Chart")
2025-09-17 18:47:02,219 INFO ipython chart1.name = "SDG Metrics Mapping Chart"
2025-09-17 18:47:02,219 INFO ipython chart1.chart_name = "SDG Metrics Mapping Chart"
2025-09-17 18:47:02,219 INFO ipython chart1.chart_type = "Report"
2025-09-17 18:47:02,219 INFO ipython chart1.report_name = "SDG Metrics Mapping"
2025-09-17 18:47:02,219 INFO ipython chart1.type = "Bar"
2025-09-17 18:47:02,219 INFO ipython chart1.color = "#3b82f6"
2025-09-17 18:47:02,219 INFO ipython chart1.is_public = 1
2025-09-17 18:47:02,219 INFO ipython chart1.module = "Sdg Reporting"
2025-09-17 18:47:02,219 INFO ipython chart1.is_standard = 1
2025-09-17 18:47:02,220 INFO ipython chart1.insert(ignore_permissions=True)
2025-09-17 18:47:02,220 INFO ipython print("✅ Created Dashboard Chart: SDG Metrics Mapping Chart")
2025-09-17 18:47:02,220 INFO ipython # Create Dashboard Chart for SDG Metrics Mapping with filters_json
2025-09-17 18:47:02,220 INFO ipython chart1 = frappe.new_doc("Dashboard Chart")
2025-09-17 18:47:02,220 INFO ipython chart1.name = "SDG Metrics Mapping Chart"
2025-09-17 18:47:02,220 INFO ipython chart1.chart_name = "SDG Metrics Mapping Chart"
2025-09-17 18:47:02,220 INFO ipython chart1.chart_type = "Report"
2025-09-17 18:47:02,220 INFO ipython chart1.report_name = "SDG Metrics Mapping"
2025-09-17 18:47:02,220 INFO ipython chart1.type = "Bar"
2025-09-17 18:47:02,221 INFO ipython chart1.color = "#3b82f6"
2025-09-17 18:47:02,221 INFO ipython chart1.is_public = 1
2025-09-17 18:47:02,221 INFO ipython chart1.module = "Sdg Reporting"
2025-09-17 18:47:02,221 INFO ipython chart1.is_standard = 1
2025-09-17 18:47:02,221 INFO ipython chart1.filters_json = "[]"
2025-09-17 18:47:02,221 INFO ipython chart1.insert(ignore_permissions=True)
2025-09-17 18:47:02,221 INFO ipython print("✅ Created Dashboard Chart: SDG Metrics Mapping Chart")
2025-09-17 18:47:02,221 INFO ipython # Create Dashboard Chart for SDG Progress Tracking
2025-09-17 18:47:02,221 INFO ipython chart2 = frappe.new_doc("Dashboard Chart")
2025-09-17 18:47:02,222 INFO ipython chart2.name = "SDG Progress Tracking Chart"
2025-09-17 18:47:02,222 INFO ipython chart2.chart_name = "SDG Progress Tracking Chart"
2025-09-17 18:47:02,222 INFO ipython chart2.chart_type = "Report"
2025-09-17 18:47:02,222 INFO ipython chart2.report_name = "SDG Progress Tracking"
2025-09-17 18:47:02,222 INFO ipython chart2.type = "Line"
2025-09-17 18:47:02,222 INFO ipython chart2.color = "#10b981"
2025-09-17 18:47:02,222 INFO ipython chart2.is_public = 1
2025-09-17 18:47:02,222 INFO ipython chart2.module = "Sdg Reporting"
2025-09-17 18:47:02,222 INFO ipython chart2.is_standard = 1
2025-09-17 18:47:02,222 INFO ipython chart2.filters_json = "[]"
2025-09-17 18:47:02,223 INFO ipython chart2.insert(ignore_permissions=True)
2025-09-17 18:47:02,223 INFO ipython print("✅ Created Dashboard Chart: SDG Progress Tracking Chart")
2025-09-17 18:47:02,223 INFO ipython # Create Dashboard Chart for SDG Goal Distribution
2025-09-17 18:47:02,223 INFO ipython chart3 = frappe.new_doc("Dashboard Chart")
2025-09-17 18:47:02,223 INFO ipython chart3.name = "SDG Goal Distribution Chart"
2025-09-17 18:47:02,223 INFO ipython chart3.chart_name = "SDG Goal Distribution Chart"
2025-09-17 18:47:02,223 INFO ipython chart3.chart_type = "Report"
2025-09-17 18:47:02,223 INFO ipython chart3.report_name = "SDG Goal Distribution"
2025-09-17 18:47:02,224 INFO ipython chart3.type = "Pie"
2025-09-17 18:47:02,224 INFO ipython chart3.color = "#ef4444"
2025-09-17 18:47:02,225 INFO ipython chart3.is_public = 1
2025-09-17 18:47:02,225 INFO ipython chart3.module = "Sdg Reporting"
2025-09-17 18:47:02,225 INFO ipython chart3.is_standard = 1
2025-09-17 18:47:02,225 INFO ipython chart3.filters_json = "[]"
2025-09-17 18:47:02,225 INFO ipython chart3.insert(ignore_permissions=True)
2025-09-17 18:47:02,225 INFO ipython print("✅ Created Dashboard Chart: SDG Goal Distribution Chart")
2025-09-17 18:47:02,225 INFO ipython # Create Number Cards
2025-09-17 18:47:02,226 INFO ipython card1 = frappe.new_doc("Number Card")
2025-09-17 18:47:02,226 INFO ipython card1.name = "Total SDG Metrics"
2025-09-17 18:47:02,226 INFO ipython card1.label = "Total SDG Metrics"
2025-09-17 18:47:02,226 INFO ipython card1.type = "Document Type"
2025-09-17 18:47:02,226 INFO ipython card1.document_type = "Sustainability Metric"
2025-09-17 18:47:02,226 INFO ipython card1.function = "Count"
2025-09-17 18:47:02,226 INFO ipython card1.color = "#3b82f6"
2025-09-17 18:47:02,226 INFO ipython card1.is_public = 1
2025-09-17 18:47:02,227 INFO ipython card1.module = "Sdg Reporting"
2025-09-17 18:47:02,227 INFO ipython card1.is_standard = 1
2025-09-17 18:47:02,227 INFO ipython card1.insert(ignore_permissions=True)
2025-09-17 18:47:02,227 INFO ipython print("✅ Created Number Card: Total SDG Metrics")
2025-09-17 18:47:02,227 INFO ipython # Create SDG Mapping Dashboard
2025-09-17 18:47:02,227 INFO ipython dashboard1 = frappe.new_doc("Dashboard")
2025-09-17 18:47:02,227 INFO ipython dashboard1.name = "SDG Mapping Dashboard"
2025-09-17 18:47:02,227 INFO ipython dashboard1.dashboard_name = "SDG Mapping Dashboard"
2025-09-17 18:47:02,227 INFO ipython dashboard1.module = "Sdg Reporting"
2025-09-17 18:47:02,228 INFO ipython dashboard1.is_standard = 1
2025-09-17 18:47:02,228 INFO ipython # Add chart
2025-09-17 18:47:02,228 INFO ipython chart_row = dashboard1.append("charts")
2025-09-17 18:47:02,228 INFO ipython chart_row.chart = "SDG Metrics Mapping Chart"
2025-09-17 18:47:02,228 INFO ipython chart_row.width = "Full"
2025-09-17 18:47:02,228 INFO ipython # Add card
2025-09-17 18:47:02,228 INFO ipython card_row = dashboard1.append("cards")
2025-09-17 18:47:02,228 INFO ipython card_row.card = "Total SDG Metrics"
2025-09-17 18:47:02,228 INFO ipython dashboard1.insert(ignore_permissions=True)
2025-09-17 18:47:02,228 INFO ipython print("✅ Created Dashboard: SDG Mapping Dashboard")
2025-09-17 18:47:02,229 INFO ipython # Create SDG Progress Dashboard
2025-09-17 18:47:02,229 INFO ipython dashboard2 = frappe.new_doc("Dashboard")
2025-09-17 18:47:02,229 INFO ipython dashboard2.name = "SDG Progress Dashboard"
2025-09-17 18:47:02,229 INFO ipython dashboard2.dashboard_name = "SDG Progress Dashboard"
2025-09-17 18:47:02,229 INFO ipython dashboard2.module = "Sdg Reporting"
2025-09-17 18:47:02,229 INFO ipython dashboard2.is_standard = 1
2025-09-17 18:47:02,229 INFO ipython # Add chart
2025-09-17 18:47:02,229 INFO ipython chart_row = dashboard2.append("charts")
2025-09-17 18:47:02,229 INFO ipython chart_row.chart = "SDG Progress Tracking Chart"
2025-09-17 18:47:02,230 INFO ipython chart_row.width = "Full"
2025-09-17 18:47:02,230 INFO ipython # Add card
2025-09-17 18:47:02,230 INFO ipython card_row = dashboard2.append("cards")
2025-09-17 18:47:02,230 INFO ipython card_row.card = "Total SDG Metrics"
2025-09-17 18:47:02,230 INFO ipython dashboard2.insert(ignore_permissions=True)
2025-09-17 18:47:02,230 INFO ipython print("✅ Created Dashboard: SDG Progress Dashboard")
2025-09-17 18:47:02,230 INFO ipython # Create SDG Distribution Dashboard
2025-09-17 18:47:02,230 INFO ipython dashboard3 = frappe.new_doc("Dashboard")
2025-09-17 18:47:02,230 INFO ipython dashboard3.name = "SDG Distribution Dashboard"
2025-09-17 18:47:02,230 INFO ipython dashboard3.dashboard_name = "SDG Distribution Dashboard"
2025-09-17 18:47:02,231 INFO ipython dashboard3.module = "Sdg Reporting"
2025-09-17 18:47:02,231 INFO ipython dashboard3.is_standard = 1
2025-09-17 18:47:02,231 INFO ipython # Add chart
2025-09-17 18:47:02,231 INFO ipython chart_row = dashboard3.append("charts")
2025-09-17 18:47:02,231 INFO ipython chart_row.chart = "SDG Goal Distribution Chart"
2025-09-17 18:47:02,232 INFO ipython chart_row.width = "Full"
2025-09-17 18:47:02,232 INFO ipython # Add card
2025-09-17 18:47:02,232 INFO ipython card_row = dashboard3.append("cards")
2025-09-17 18:47:02,232 INFO ipython card_row.card = "Total SDG Metrics"
2025-09-17 18:47:02,232 INFO ipython dashboard3.insert(ignore_permissions=True)
2025-09-17 18:47:02,232 INFO ipython print("✅ Created Dashboard: SDG Distribution Dashboard")
2025-09-17 18:47:02,232 INFO ipython frappe.db.commit()
2025-09-17 18:47:02,232 INFO ipython print("✅ All changes committed to database!")
2025-09-17 18:47:02,232 INFO ipython print("\n🎯 SDG Dashboards Successfully Created!")
2025-09-17 18:47:02,233 INFO ipython print("\nAvailable Dashboards:")
2025-09-17 18:47:02,233 INFO ipython print("1. SDG Mapping Dashboard - Shows metrics mapped to SDG targets, goals, ESG pillars")
2025-09-17 18:47:02,233 INFO ipython print("2. SDG Progress Dashboard - Displays Q1 to Q2 progress with trend analysis")
2025-09-17 18:47:02,233 INFO ipython print("3. SDG Distribution Dashboard - Shows goal distribution and employee impact")
2025-09-17 18:47:02,233 INFO ipython print("\nAccess them at:")
2025-09-17 18:47:02,233 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Mapping%20Dashboard")
2025-09-17 18:47:02,233 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Progress%20Dashboard")
2025-09-17 18:47:02,233 INFO ipython print("- http://localhost:8000/app/dashboard-view/SDG%20Distribution%20Dashboard")
2025-09-17 18:47:02,233 INFO ipython === session end ===
2025-09-17 18:58:46,229 INFO ipython === bench console session ===
2025-09-17 18:58:46,230 INFO ipython # Fix the dashboard charts by changing them to use document-based charts instead of report-based
2025-09-17 18:58:46,230 INFO ipython import frappe
2025-09-17 18:58:46,230 INFO ipython # Delete the existing report-based charts
2025-09-17 18:58:46,230 INFO ipython charts_to_delete = [
    "SDG Metrics Mapping Chart",
        "SDG Progress Tracking Chart", 
            "SDG Goal Distribution Chart"
            ]
2025-09-17 18:58:46,230 INFO ipython for chart_name in charts_to_delete:
        if frappe.db.exists("Dashboard Chart", chart_name):
                    frappe.delete_doc("Dashboard Chart", chart_name, ignore_permissions=True)
                            print(f"Deleted chart: {chart_name}")
2025-09-17 18:58:46,230 INFO ipython print("Existing charts deleted. Creating new document-based charts...")
2025-09-17 18:58:46,231 INFO ipython # Delete charts one by one
2025-09-17 18:58:46,231 INFO ipython if frappe.db.exists("Dashboard Chart", "SDG Metrics Mapping Chart"):
        frappe.delete_doc("Dashboard Chart", "SDG Metrics Mapping Chart", ignore_permissions=True)
            print("Deleted: SDG Metrics Mapping Chart")
2025-09-17 18:58:46,231 INFO ipython if frappe.db.exists("Dashboard Chart", "SDG Progress Tracking Chart"):
        frappe.delete_doc("Dashboard Chart", "SDG Progress Tracking Chart", ignore_permissions=True)
            print("Deleted: SDG Progress Tracking Chart")
2025-09-17 18:58:46,231 INFO ipython if frappe.db.exists("Dashboard Chart", "SDG Goal Distribution Chart"):
        frappe.delete_doc("Dashboard Chart", "SDG Goal Distribution Chart", ignore_permissions=True)
            print("Deleted: SDG Goal Distribution Chart")
2025-09-17 18:58:46,231 INFO ipython frappe.delete_doc("Dashboard Chart", "SDG Metrics Mapping Chart", ignore_permissions=True)
2025-09-17 18:58:46,231 INFO ipython frappe.delete_doc("Dashboard Chart", "SDG Progress Tracking Chart", ignore_permissions=True)
2025-09-17 18:58:46,231 INFO ipython frappe.delete_doc("Dashboard Chart", "SDG Goal Distribution Chart", ignore_permissions=True)
2025-09-17 18:58:46,231 INFO ipython print("Deleted all existing charts")
2025-09-17 18:58:46,231 INFO ipython # Update the existing charts to use document-based chart types instead of report-based
2025-09-17 18:58:46,231 INFO ipython # Update SDG Metrics Mapping Chart to use Group By
2025-09-17 18:58:46,232 INFO ipython chart1 = frappe.get_doc("Dashboard Chart", "SDG Metrics Mapping Chart")
2025-09-17 18:58:46,232 INFO ipython chart1.chart_type = "Group By"
2025-09-17 18:58:46,232 INFO ipython chart1.document_type = "Sustainability Metric"
2025-09-17 18:58:46,232 INFO ipython chart1.based_on = "esg_pillar"
2025-09-17 18:58:46,232 INFO ipython chart1.value_based_on = "name"
2025-09-17 18:58:46,232 INFO ipython chart1.group_by_type = "Count"
2025-09-17 18:58:46,232 INFO ipython chart1.group_by_based_on = "esg_pillar"
2025-09-17 18:58:46,232 INFO ipython chart1.report_name = ""  # Clear report name
2025-09-17 18:58:46,232 INFO ipython chart1.save(ignore_permissions=True)
2025-09-17 18:58:46,232 INFO ipython print("✅ Updated SDG Metrics Mapping Chart to Group By chart")
2025-09-17 18:58:46,233 INFO ipython # Create new working charts with different names
2025-09-17 18:58:46,233 INFO ipython # 1. ESG Pillar Distribution Chart (Group By)
2025-09-17 18:58:46,233 INFO ipython chart1 = frappe.new_doc("Dashboard Chart")
2025-09-17 18:58:46,233 INFO ipython chart1.name = "ESG Pillar Distribution"
2025-09-17 18:58:46,233 INFO ipython chart1.chart_name = "ESG Pillar Distribution"
2025-09-17 18:58:46,233 INFO ipython chart1.chart_type = "Group By"
2025-09-17 18:58:46,233 INFO ipython chart1.document_type = "Sustainability Metric"
2025-09-17 18:58:46,233 INFO ipython chart1.based_on = "esg_pillar"
2025-09-17 18:58:46,233 INFO ipython chart1.value_based_on = "name"
2025-09-17 18:58:46,234 INFO ipython chart1.group_by_type = "Count"
2025-09-17 18:58:46,234 INFO ipython chart1.group_by_based_on = "esg_pillar"
2025-09-17 18:58:46,234 INFO ipython chart1.type = "Bar"
2025-09-17 18:58:46,234 INFO ipython chart1.color = "#3b82f6"
2025-09-17 18:58:46,234 INFO ipython chart1.is_public = 1
2025-09-17 18:58:46,234 INFO ipython chart1.module = "Sdg Reporting"
2025-09-17 18:58:46,234 INFO ipython chart1.is_standard = 1
2025-09-17 18:58:46,234 INFO ipython chart1.filters_json = "[]"
2025-09-17 18:58:46,234 INFO ipython chart1.insert(ignore_permissions=True)
2025-09-17 18:58:46,234 INFO ipython print("✅ Created ESG Pillar Distribution Chart")
2025-09-17 18:58:46,234 INFO ipython # 2. SDG Goals Distribution Chart (Group By)
2025-09-17 18:58:46,235 INFO ipython chart2 = frappe.new_doc("Dashboard Chart")
2025-09-17 18:58:46,235 INFO ipython chart2.name = "SDG Goals Distribution"
2025-09-17 18:58:46,235 INFO ipython chart2.chart_name = "SDG Goals Distribution"
2025-09-17 18:58:46,235 INFO ipython chart2.chart_type = "Group By"
2025-09-17 18:58:46,235 INFO ipython chart2.document_type = "Sustainability Metric"
2025-09-17 18:58:46,235 INFO ipython chart2.based_on = "sdg_link"
2025-09-17 18:58:46,235 INFO ipython chart2.value_based_on = "name"
2025-09-17 18:58:46,235 INFO ipython chart2.group_by_type = "Count"
2025-09-17 18:58:46,235 INFO ipython chart2.group_by_based_on = "sdg_link"
2025-09-17 18:58:46,236 INFO ipython chart2.type = "Pie"
2025-09-17 18:58:46,236 INFO ipython chart2.color = "#10b981"
2025-09-17 18:58:46,236 INFO ipython chart2.is_public = 1
2025-09-17 18:58:46,236 INFO ipython chart2.module = "Sdg Reporting"
2025-09-17 18:58:46,236 INFO ipython chart2.is_standard = 1
2025-09-17 18:58:46,236 INFO ipython chart2.filters_json = "[]"
2025-09-17 18:58:46,236 INFO ipython chart2.insert(ignore_permissions=True)
2025-09-17 18:58:46,236 INFO ipython print("✅ Created SDG Goals Distribution Chart")
2025-09-17 18:58:46,236 INFO ipython # 3. Sustainability Entries Trend Chart (Count with timeseries)
2025-09-17 18:58:46,236 INFO ipython chart3 = frappe.new_doc("Dashboard Chart")
2025-09-17 18:58:46,237 INFO ipython chart3.name = "Sustainability Entries Trend"
2025-09-17 18:58:46,237 INFO ipython chart3.chart_name = "Sustainability Entries Trend"
2025-09-17 18:58:46,237 INFO ipython chart3.chart_type = "Count"
2025-09-17 18:58:46,237 INFO ipython chart3.document_type = "Sustainability Entry"
2025-09-17 18:58:46,237 INFO ipython chart3.based_on = "reporting_period"
2025-09-17 18:58:46,237 INFO ipython chart3.timeseries = 1
2025-09-17 18:58:46,237 INFO ipython chart3.time_interval = "Monthly"
2025-09-17 18:58:46,237 INFO ipython chart3.timespan = "Last Year"
2025-09-17 18:58:46,237 INFO ipython chart3.type = "Line"
2025-09-17 18:58:46,237 INFO ipython chart3.color = "#ef4444"
2025-09-17 18:58:46,237 INFO ipython chart3.is_public = 1
2025-09-17 18:58:46,237 INFO ipython chart3.module = "Sdg Reporting"
2025-09-17 18:58:46,237 INFO ipython chart3.is_standard = 1
2025-09-17 18:58:46,237 INFO ipython chart3.filters_json = "[]"
2025-09-17 18:58:46,237 INFO ipython chart3.insert(ignore_permissions=True)
2025-09-17 18:58:46,238 INFO ipython print("✅ Created Sustainability Entries Trend Chart")
2025-09-17 18:58:46,238 INFO ipython # Update the dashboards to use the new working charts
2025-09-17 18:58:46,238 INFO ipython # Update SDG Mapping Dashboard
2025-09-17 18:58:46,238 INFO ipython dashboard1 = frappe.get_doc("Dashboard", "SDG Mapping Dashboard")
2025-09-17 18:58:46,238 INFO ipython # Clear existing charts and add new ones
2025-09-17 18:58:46,238 INFO ipython dashboard1.charts = []
2025-09-17 18:58:46,238 INFO ipython chart_row = dashboard1.append("charts")
2025-09-17 18:58:46,238 INFO ipython chart_row.chart = "ESG Pillar Distribution"
2025-09-17 18:58:46,238 INFO ipython chart_row.width = "Full"
2025-09-17 18:58:46,238 INFO ipython dashboard1.save(ignore_permissions=True)
2025-09-17 18:58:46,238 INFO ipython print("✅ Updated SDG Mapping Dashboard")
2025-09-17 18:58:46,238 INFO ipython # Update SDG Progress Dashboard
2025-09-17 18:58:46,239 INFO ipython dashboard2 = frappe.get_doc("Dashboard", "SDG Progress Dashboard")
2025-09-17 18:58:46,239 INFO ipython dashboard2.charts = []
2025-09-17 18:58:46,239 INFO ipython chart_row = dashboard2.append("charts")
2025-09-17 18:58:46,239 INFO ipython chart_row.chart = "Sustainability Entries Trend"
2025-09-17 18:58:46,239 INFO ipython chart_row.width = "Full"
2025-09-17 18:58:46,239 INFO ipython dashboard2.save(ignore_permissions=True)
2025-09-17 18:58:46,239 INFO ipython print("✅ Updated SDG Progress Dashboard")
2025-09-17 18:58:46,239 INFO ipython # Update SDG Distribution Dashboard
2025-09-17 18:58:46,239 INFO ipython dashboard3 = frappe.get_doc("Dashboard", "SDG Distribution Dashboard")
2025-09-17 18:58:46,239 INFO ipython dashboard3.charts = []
2025-09-17 18:58:46,239 INFO ipython chart_row = dashboard3.append("charts")
2025-09-17 18:58:46,240 INFO ipython chart_row.chart = "SDG Goals Distribution"
2025-09-17 18:58:46,240 INFO ipython chart_row.width = "Full"
2025-09-17 18:58:46,240 INFO ipython dashboard3.save(ignore_permissions=True)
2025-09-17 18:58:46,240 INFO ipython print("✅ Updated SDG Distribution Dashboard")
2025-09-17 18:58:46,240 INFO ipython # Create some sample data to make the charts work
2025-09-17 18:58:46,240 INFO ipython import frappe
2025-09-17 18:58:46,240 INFO ipython from datetime import datetime, timedelta
2025-09-17 18:58:46,240 INFO ipython # Create sample Sustainability Metrics if they don't exist
